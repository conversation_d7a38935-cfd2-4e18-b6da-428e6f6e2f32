<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- <PERSON>er -->
    <div class="bg-gradient-to-r from-purple-900 via-pink-900 to-red-900 rounded-2xl shadow-xl p-8 text-white relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full"></div>
            <div class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full"></div>
        </div>
        
        <div class="relative z-10 flex items-center justify-between">
            <div>
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-4">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M7.5,4A5.5,5.5 0 0,0 2,9.5C2,10 2.04,10.5 2.12,11H6.5L7.66,8.5H8.34L9.5,11H13.88C13.96,10.5 14,10 14,9.5A5.5,5.5 0 0,0 8.5,4H7.5M21.8,12H2.2L1.27,21H22.73L21.8,12Z"/>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold">Game Library</h1>
                        <p class="text-purple-100 text-lg">Discover amazing PlayStation games</p>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <?php if(auth()->user()->isAdmin()): ?>
                    <a href="<?php echo e(route('admin.games.create')); ?>" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 border border-white/30">
                        <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19,*************************************"/>
                        </svg>
                        Add Game
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-sm p-6">
        <form method="GET" action="<?php echo e(route(auth()->user()->isAdmin() ? 'admin.games.filter' : 'user.games.index')); ?>" class="flex flex-wrap items-center gap-4">
            <div class="flex-1 min-w-48">
                <label for="platform" class="block text-sm font-medium text-gray-700 mb-1">Platform</label>
                <select name="platform" id="platform" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
                    <option value="all">All Platforms</option>
                    <option value="PS5" <?php echo e(request('platform') === 'PS5' ? 'selected' : ''); ?>>PlayStation 5</option>
                    <option value="PS4" <?php echo e(request('platform') === 'PS4' ? 'selected' : ''); ?>>PlayStation 4</option>
                    <option value="Both" <?php echo e(request('platform') === 'Both' ? 'selected' : ''); ?>>Both</option>
                </select>
            </div>
            
            <div class="flex-1 min-w-48">
                <label for="genre" class="block text-sm font-medium text-gray-700 mb-1">Genre</label>
                <select name="genre" id="genre" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
                    <option value="all">All Genres</option>
                    <option value="Action/Adventure" <?php echo e(request('genre') === 'Action/Adventure' ? 'selected' : ''); ?>>Action/Adventure</option>
                    <option value="Action RPG" <?php echo e(request('genre') === 'Action RPG' ? 'selected' : ''); ?>>Action RPG</option>
                    <option value="Sports" <?php echo e(request('genre') === 'Sports' ? 'selected' : ''); ?>>Sports</option>
                    <option value="FPS" <?php echo e(request('genre') === 'FPS' ? 'selected' : ''); ?>>FPS</option>
                    <option value="Platformer" <?php echo e(request('genre') === 'Platformer' ? 'selected' : ''); ?>>Platformer</option>
                    <option value="Sandbox" <?php echo e(request('genre') === 'Sandbox' ? 'selected' : ''); ?>>Sandbox</option>
                </select>
            </div>
            
            <div class="flex items-end">
                <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200">
                    Filter
                </button>
            </div>
        </form>
    </div>

    <!-- Games Grid -->
    <div class="bg-white rounded-xl shadow-sm">
        <?php if($games->count() > 0): ?>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <?php $__currentLoopData = $games; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $game): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-200 hover:shadow-lg transition-all duration-200 hover:scale-105">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M7.5,4A5.5,5.5 0 0,0 2,9.5C2,10 2.04,10.5 2.12,11H6.5L7.66,8.5H8.34L9.5,11H13.88C13.96,10.5 14,10 14,9.5A5.5,5.5 0 0,0 8.5,4H7.5M21.8,12H2.2L1.27,21H22.73L21.8,12Z"/>
                                    </svg>
                                </div>
                                
                                <h3 class="font-bold text-gray-900 text-lg mb-2"><?php echo e($game->title); ?></h3>
                                <p class="text-sm text-gray-600 mb-3"><?php echo e($game->genre); ?></p>
                                
                                <!-- Rating -->
                                <div class="flex items-center justify-center space-x-1 mb-3">
                                    <?php if($game->rating_score): ?>
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <?php if($i <= ($game->rating_score / 2)): ?>
                                                <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                </svg>
                                            <?php else: ?>
                                                <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                </svg>
                                            <?php endif; ?>
                                        <?php endfor; ?>
                                        <span class="text-sm text-gray-600 ml-1">(<?php echo e($game->rating_score); ?>)</span>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Platform & Price -->
                                <div class="space-y-2 mb-4">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        <?php echo e($game->platform); ?>

                                    </span>
                                    <?php if($game->is_popular): ?>
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 ml-2">
                                            Popular
                                        </span>
                                    <?php endif; ?>
                                    <div class="text-lg font-bold text-purple-600">
                                        +Rp <?php echo e(number_format($game->rental_price)); ?>/hr
                                    </div>
                                </div>
                                
                                <!-- Stock -->
                                <div class="text-sm text-gray-600 mb-4">
                                    Stock: <?php echo e($game->available_stock); ?>/<?php echo e($game->stock); ?>

                                </div>
                                
                                <!-- Actions -->
                                <div class="flex items-center justify-center space-x-2">
                                    <?php if(auth()->user()->isAdmin()): ?>
                                        <a href="<?php echo e(route('admin.games.show', $game)); ?>" class="text-blue-600 hover:text-blue-700 text-sm font-medium">View</a>
                                        <a href="<?php echo e(route('admin.games.edit', $game)); ?>" class="text-indigo-600 hover:text-indigo-700 text-sm font-medium">Edit</a>
                                    <?php else: ?>
                                        <a href="<?php echo e(route('user.games.show', $game)); ?>" class="text-blue-600 hover:text-blue-700 text-sm font-medium">View Details</a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>

            <!-- Pagination -->
            <?php if($games->hasPages()): ?>
                <div class="px-6 py-4 border-t border-gray-200">
                    <?php echo e($games->links()); ?>

                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="text-center py-12">
                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M7.5,4A5.5,5.5 0 0,0 2,9.5C2,10 2.04,10.5 2.12,11H6.5L7.66,8.5H8.34L9.5,11H13.88C13.96,10.5 14,10 14,9.5A5.5,5.5 0 0,0 8.5,4H7.5M21.8,12H2.2L1.27,21H22.73L21.8,12Z"/>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No games found</h3>
                <p class="text-gray-500 mb-4">No games match your current filters.</p>
                <?php if(auth()->user()->isAdmin()): ?>
                    <a href="<?php echo e(route('admin.games.create')); ?>" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                        Add First Game
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\TokoRentalPlayStation\resources\views/games/index.blade.php ENDPATH**/ ?>