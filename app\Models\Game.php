<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Game extends Model
{
    protected $fillable = [
        'title',
        'genre',
        'description',
        'image',
        'platform',
        'rating',
        'release_year',
        'rental_price',
        'stock',
        'available_stock',
        'is_popular',
        'rating_score',
    ];

    protected $casts = [
        'rental_price' => 'decimal:2',
        'rating_score' => 'decimal:1',
        'is_popular' => 'boolean',
    ];

    /**
     * Check if game is available
     */
    public function isAvailable(): bool
    {
        return $this->available_stock > 0;
    }

    /**
     * Get games by platform
     */
    public function scopeForPlatform($query, $platform)
    {
        return $query->where('platform', $platform)
                    ->orWhere('platform', 'Both');
    }

    /**
     * Get popular games
     */
    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    /**
     * Get games by genre
     */
    public function scopeByGenre($query, $genre)
    {
        return $query->where('genre', $genre);
    }
}
