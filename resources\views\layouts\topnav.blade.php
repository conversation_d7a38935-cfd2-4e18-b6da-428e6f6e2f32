<!-- Top Navigation -->
<header class="bg-white shadow-sm border-b border-gray-200 lg:hidden">
    <div class="flex items-center justify-between px-4 py-3">
        <!-- Mobile menu button -->
        <button id="mobile-menu-button" type="button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500">
            <span class="sr-only">Open main menu</span>
            <svg class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
        </button>

        <!-- Logo -->
        <div class="flex items-center">
            <x-application-logo class="block h-8 w-auto fill-current text-blue-600" />
            <span class="ml-2 text-lg font-semibold text-gray-900">{{ config('app.name', 'Laravel') }}</span>
        </div>

        <!-- User menu -->
        <div class="flex items-center">
            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                <span class="text-white text-sm font-medium">{{ substr(auth()->user()->name, 0, 1) }}</span>
            </div>
        </div>
    </div>
</header>

<!-- Desktop top bar (optional, for breadcrumbs or additional info) -->
<div class="hidden lg:block bg-white border-b border-gray-200">
    <div class="px-6 py-2">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2 text-sm text-gray-500">
                @if(auth()->user()->isAdmin())
                    <span>Admin Panel</span>
                @else
                    <span>User Dashboard</span>
                @endif
                @if(request()->routeIs('*.posts.*'))
                    <span>/</span>
                    <span>Posts</span>
                @endif
            </div>
            
            <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-600">Welcome, {{ auth()->user()->name }}</span>
                <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full capitalize">
                    {{ auth()->user()->role }}
                </span>
            </div>
        </div>
    </div>
</div>
