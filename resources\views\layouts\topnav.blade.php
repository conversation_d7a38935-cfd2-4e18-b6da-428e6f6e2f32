<!-- Gaming Top Navigation -->
<header class="gaming-card shadow-lg border-b border-blue-200 lg:hidden glow-effect">
    <div class="flex items-center justify-between px-4 py-3">
        <!-- Gaming Mobile menu button -->
        <button id="mobile-menu-button" type="button" class="inline-flex items-center justify-center p-2 rounded-md text-blue-600 hover:text-blue-800 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-all duration-200 pulse-glow">
            <span class="sr-only">Open main menu</span>
            <svg class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
        </button>

        <!-- Gaming Logo -->
        <div class="flex items-center">
            <div class="w-8 h-8 playstation-gradient rounded-lg flex items-center justify-center mr-2 floating">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
            </div>
            <span class="text-lg font-bold text-gray-900 gaming-font neon-text">🎮 PlayStation Rental</span>
        </div>

        <!-- Gaming User menu -->
        <div class="flex items-center">
            <div class="w-8 h-8 playstation-gradient rounded-full flex items-center justify-center pulse-glow">
                <span class="text-white text-sm font-medium gaming-font">{{ substr(auth()->user()->name, 0, 1) }}</span>
            </div>
        </div>
    </div>
</header>

<!-- Desktop top bar (optional, for breadcrumbs or additional info) -->
<div class="hidden lg:block bg-white border-b border-gray-200">
    <div class="px-6 py-2">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2 text-sm text-gray-500">
                @if(auth()->user()->isAdmin())
                    <span>Admin Panel</span>
                @else
                    <span>User Dashboard</span>
                @endif
                @if(request()->routeIs('*.posts.*'))
                    <span>/</span>
                    <span>Posts</span>
                @endif
            </div>
            
            <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-600">Welcome, {{ auth()->user()->name }}</span>
                <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full capitalize">
                    {{ auth()->user()->role }}
                </span>
            </div>
        </div>
    </div>
</div>
