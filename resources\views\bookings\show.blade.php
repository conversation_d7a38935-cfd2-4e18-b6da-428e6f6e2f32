@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Gaming Header -->
    <div class="bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-900 rounded-2xl shadow-xl p-8 text-white relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full floating"></div>
            <div class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full floating" style="animation-delay: 1s;"></div>
        </div>
        
        <div class="relative z-10 flex items-center justify-between">
            <div>
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-4 pulse-glow">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold gaming-font neon-text">Booking Details</h1>
                        <p class="text-purple-100 text-lg">{{ $booking->booking_code }}</p>
                    </div>
                </div>
            </div>
            <div class="text-right">
                @if($booking->status === 'active')
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium bg-green-100 text-green-800 pulse-glow">
                        🎮 Active Gaming
                    </span>
                @elseif($booking->status === 'pending')
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium bg-yellow-100 text-yellow-800">
                        ⏳ Pending
                    </span>
                @elseif($booking->status === 'confirmed')
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium bg-blue-100 text-blue-800">
                        ✅ Confirmed
                    </span>
                @elseif($booking->status === 'completed')
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium bg-gray-100 text-gray-800">
                        🏁 Completed
                    </span>
                @else
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium bg-red-100 text-red-800">
                        ❌ {{ ucfirst($booking->status) }}
                    </span>
                @endif
            </div>
        </div>
    </div>

    <!-- Booking Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Console & Schedule Info -->
        <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
            <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
                <svg class="w-6 h-6 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M21,6H3A1,1 0 0,0 2,7V17A1,1 0 0,0 3,18H21A1,1 0 0,0 22,17V7A1,1 0 0,0 21,6M20,16H4V8H20V16Z"/>
                </svg>
                Console & Schedule
            </h2>
            
            <!-- Console Details -->
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 mb-6 border border-blue-200">
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-4 floating">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M21,6H3A1,1 0 0,0 2,7V17A1,1 0 0,0 3,18H21A1,1 0 0,0 22,17V7A1,1 0 0,0 21,6M20,16H4V8H20V16Z"/>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900">{{ $booking->console->name ?? 'Unknown Console' }}</h3>
                        <p class="text-gray-600">{{ $booking->console->type ?? 'N/A' }}</p>
                    </div>
                </div>
                
                @if($booking->console && $booking->console->specifications)
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        @foreach($booking->console->specifications as $key => $value)
                            <div class="flex justify-between">
                                <span class="text-gray-600">{{ ucfirst(str_replace('_', ' ', $key)) }}:</span>
                                <span class="font-medium text-blue-600">{{ $value }}</span>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
            
            <!-- Schedule Details -->
            <div class="space-y-4">
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <span class="text-gray-600 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
                        </svg>
                        Start Time:
                    </span>
                    <span class="font-bold text-gray-900">{{ $booking->start_time->format('d M Y, H:i') }}</span>
                </div>
                
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <span class="text-gray-600 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-red-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
                        </svg>
                        End Time:
                    </span>
                    <span class="font-bold text-gray-900">{{ $booking->end_time->format('d M Y, H:i') }}</span>
                </div>
                
                <div class="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <span class="text-blue-600 flex items-center font-medium">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6Z"/>
                        </svg>
                        Duration:
                    </span>
                    <span class="font-bold text-blue-900 text-lg">{{ $booking->duration_hours }} hours</span>
                </div>
            </div>
        </div>

        <!-- Games & Payment Info -->
        <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
            <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
                <svg class="w-6 h-6 mr-2 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M7.5,4A5.5,5.5 0 0,0 2,9.5C2,10 2.04,10.5 2.12,11H6.5L7.66,8.5H8.34L9.5,11H13.88C13.96,10.5 14,10 14,9.5A5.5,5.5 0 0,0 8.5,4H7.5M21.8,12H2.2L1.27,21H22.73L21.8,12Z"/>
                </svg>
                Games & Payment
            </h2>
            
            <!-- Selected Games -->
            @if($games && $games->count() > 0)
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Selected Games:</h3>
                    <div class="space-y-3">
                        @foreach($games as $game)
                            <div class="flex items-center p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200">
                                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3 floating">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M7.5,4A5.5,5.5 0 0,0 2,9.5C2,10 2.04,10.5 2.12,11H6.5L7.66,8.5H8.34L9.5,11H13.88C13.96,10.5 14,10 14,9.5A5.5,5.5 0 0,0 8.5,4H7.5M21.8,12H2.2L1.27,21H22.73L21.8,12Z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900">{{ $game->title }}</h4>
                                    <p class="text-sm text-gray-600">{{ $game->genre }} • {{ $game->platform }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="font-bold text-purple-600">+Rp {{ number_format($game->rental_price) }}/hr</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @else
                <div class="mb-6 text-center py-6 bg-gray-50 rounded-lg">
                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M7.5,4A5.5,5.5 0 0,0 2,9.5C2,10 2.04,10.5 2.12,11H6.5L7.66,8.5H8.34L9.5,11H13.88C13.96,10.5 14,10 14,9.5A5.5,5.5 0 0,0 8.5,4H7.5M21.8,12H2.2L1.27,21H22.73L21.8,12Z"/>
                    </svg>
                    <p class="text-gray-500">No games selected</p>
                </div>
            @endif
            
            <!-- Cost Breakdown -->
            <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 border border-green-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z"/>
                    </svg>
                    Cost Breakdown
                </h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Console Cost:</span>
                        <span class="font-medium">Rp {{ number_format($booking->console_cost) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Games Cost:</span>
                        <span class="font-medium">Rp {{ number_format($booking->games_cost) }}</span>
                    </div>
                    @if($booking->deposit > 0)
                        <div class="flex justify-between">
                            <span class="text-gray-600">Deposit:</span>
                            <span class="font-medium">Rp {{ number_format($booking->deposit) }}</span>
                        </div>
                    @endif
                    <div class="border-t border-gray-300 pt-3">
                        <div class="flex justify-between text-xl font-bold">
                            <span class="text-gray-900">Total Cost:</span>
                            <span class="text-green-600 neon-text">Rp {{ number_format($booking->total_cost) }}</span>
                        </div>
                    </div>
                    <div class="text-center">
                        @if($booking->payment_status === 'paid')
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                💳 Paid
                            </span>
                        @elseif($booking->payment_status === 'partial')
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                💰 Partial Payment
                            </span>
                        @else
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                ❌ Unpaid
                            </span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Info & Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Customer Information -->
        @if(auth()->user()->isAdmin())
            <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
                <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
                    <svg class="w-6 h-6 mr-2 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
                    </svg>
                    Customer Information
                </h2>
                
                <div class="flex items-center p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mr-4 floating">
                        <span class="text-white font-bold text-xl gaming-font">
                            {{ strtoupper(substr($booking->user->name ?? 'U', 0, 1)) }}
                        </span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900">{{ $booking->user->name ?? 'Unknown Customer' }}</h3>
                        <p class="text-gray-600">{{ $booking->user->email ?? 'N/A' }}</p>
                        <p class="text-sm text-green-600 font-medium">
                            Member since {{ $booking->user->created_at->format('M Y') }}
                        </p>
                    </div>
                </div>
            </div>
        @endif

        <!-- Actions -->
        <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
            <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
                <svg class="w-6 h-6 mr-2 text-orange-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21V20A2,2 0 0,1 19,22H5A2,2 0 0,1 3,20V19H2A1,1 0 0,1 1,18V15A1,1 0 0,1 2,14H3A7,7 0 0,1 10,7H11V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2M7.5,13A2.5,2.5 0 0,0 5,15.5A2.5,2.5 0 0,0 7.5,18A2.5,2.5 0 0,0 10,15.5A2.5,2.5 0 0,0 7.5,13M16.5,13A2.5,2.5 0 0,0 14,15.5A2.5,2.5 0 0,0 16.5,18A2.5,2.5 0 0,0 19,15.5A2.5,2.5 0 0,0 16.5,13Z"/>
                </svg>
                Actions
            </h2>
            
            <div class="space-y-4">
                @if(auth()->user()->isAdmin())
                    <!-- Admin Actions -->
                    @if($booking->status === 'pending')
                        <form action="{{ route('admin.bookings.confirm', $booking) }}" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 pulse-glow">
                                ✅ Confirm Booking
                            </button>
                        </form>
                    @endif
                    
                    @if($booking->canCheckIn())
                        <form action="{{ route('admin.bookings.check-in', $booking) }}" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105">
                                🎮 Check In Customer
                            </button>
                        </form>
                    @endif
                    
                    @if($booking->canCheckOut())
                        <form action="{{ route('admin.bookings.check-out', $booking) }}" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="w-full bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105">
                                🏁 Check Out Customer
                            </button>
                        </form>
                    @endif
                    
                    @if($booking->status === 'pending')
                        <a href="{{ route('admin.bookings.edit', $booking) }}" class="block w-full bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white px-6 py-3 rounded-lg font-semibold text-center transition-all duration-200 transform hover:scale-105">
                            ✏️ Edit Booking
                        </a>
                    @endif
                @else
                    <!-- User Actions -->
                    @if($booking->status === 'pending')
                        <a href="{{ route('user.bookings.edit', $booking) }}" class="block w-full bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white px-6 py-3 rounded-lg font-semibold text-center transition-all duration-200 transform hover:scale-105">
                            ✏️ Edit Booking
                        </a>
                        
                        <form action="{{ route('user.bookings.cancel', $booking) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to cancel this booking?')">
                            @csrf
                            <button type="submit" class="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105">
                                ❌ Cancel Booking
                            </button>
                        </form>
                    @endif
                @endif
                
                <!-- Back Button -->
                <a href="{{ route(auth()->user()->isAdmin() ? 'admin.bookings.index' : 'user.bookings.index') }}" class="block w-full bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-6 py-3 rounded-lg font-semibold text-center transition-all duration-200">
                    ← Back to Bookings
                </a>
            </div>
        </div>
    </div>

    @if($booking->notes)
        <!-- Notes -->
        <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
            <h2 class="text-xl font-bold text-gray-900 mb-4 gaming-font flex items-center">
                <svg class="w-6 h-6 mr-2 text-yellow-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                </svg>
                Notes
            </h2>
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p class="text-gray-700">{{ $booking->notes }}</p>
            </div>
        </div>
    @endif
</div>
@endsection
