<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('games', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('genre');
            $table->text('description')->nullable();
            $table->string('image')->nullable();
            $table->string('platform'); // PS4, PS5, Both
            $table->enum('rating', ['E', 'T', 'M', 'AO'])->default('E'); // ESRB Rating
            $table->year('release_year');
            $table->decimal('rental_price', 8, 2)->default(0); // Harga rental tambahan
            $table->integer('stock')->default(1);
            $table->integer('available_stock')->default(1);
            $table->boolean('is_popular')->default(false);
            $table->decimal('rating_score', 3, 1)->nullable(); // 0.0 - 10.0
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('games');
    }
};
