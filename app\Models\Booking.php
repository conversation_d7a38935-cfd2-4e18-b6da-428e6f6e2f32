<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Booking extends Model
{
    protected $fillable = [
        'booking_code',
        'user_id',
        'console_id',
        'game_ids',
        'start_time',
        'end_time',
        'duration_hours',
        'console_cost',
        'games_cost',
        'total_cost',
        'deposit',
        'status',
        'payment_status',
        'notes',
        'checked_in_at',
        'checked_out_at',
    ];

    protected $casts = [
        'game_ids' => 'array',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'console_cost' => 'decimal:2',
        'games_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'deposit' => 'decimal:2',
        'checked_in_at' => 'datetime',
        'checked_out_at' => 'datetime',
    ];

    /**
     * Get the user that owns the booking.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the console for the booking.
     */
    public function console(): BelongsTo
    {
        return $this->belongsTo(Console::class);
    }

    /**
     * Get the games for the booking.
     */
    public function games()
    {
        if (!$this->game_ids) {
            return collect();
        }

        return Game::whereIn('id', $this->game_ids)->get();
    }

    /**
     * Generate unique booking code
     */
    public static function generateBookingCode(): string
    {
        do {
            $code = 'PS' . date('Ymd') . strtoupper(substr(uniqid(), -4));
        } while (self::where('booking_code', $code)->exists());

        return $code;
    }

    /**
     * Check if booking is active
     */
    public function isActive(): bool
    {
        return $this->status === 'active' &&
               $this->start_time <= now() &&
               $this->end_time >= now();
    }

    /**
     * Check if booking can be checked in
     */
    public function canCheckIn(): bool
    {
        return $this->status === 'confirmed' &&
               $this->start_time <= now()->addMinutes(15); // 15 minutes grace period
    }

    /**
     * Check if booking can be checked out
     */
    public function canCheckOut(): bool
    {
        return $this->status === 'active' && $this->checked_in_at;
    }

    /**
     * Calculate remaining time
     */
    public function getRemainingTime(): int
    {
        if (!$this->isActive()) {
            return 0;
        }

        return max(0, $this->end_time->diffInMinutes(now()));
    }
}
