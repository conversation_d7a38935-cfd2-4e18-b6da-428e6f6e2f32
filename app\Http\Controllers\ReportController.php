<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Booking;
use App\Models\Console;
use App\Models\Game;
use App\Models\Customer;
use App\Models\User;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Show reports dashboard
     */
    public function index()
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        // Revenue reports
        $todayRevenue = Booking::whereDate('start_time', today())
            ->where('payment_status', 'paid')
            ->sum('total_cost');

        $weeklyRevenue = Booking::whereBetween('start_time', [now()->startOfWeek(), now()->endOfWeek()])
            ->where('payment_status', 'paid')
            ->sum('total_cost');

        $monthlyRevenue = Booking::whereMonth('start_time', now()->month)
            ->where('payment_status', 'paid')
            ->sum('total_cost');

        $yearlyRevenue = Booking::whereYear('start_time', now()->year)
            ->where('payment_status', 'paid')
            ->sum('total_cost');

        // Booking statistics
        $totalBookings = Booking::count();
        $completedBookings = Booking::where('status', 'completed')->count();
        $cancelledBookings = Booking::where('status', 'cancelled')->count();
        $activeBookings = Booking::where('status', 'active')->count();

        // Console utilization
        $consoleUtilization = Console::withCount(['bookings as total_bookings'])
            ->orderBy('total_bookings', 'desc')
            ->get();

        // Popular games
        $popularGames = Game::where('is_popular', true)
            ->orderBy('rating_score', 'desc')
            ->take(10)
            ->get();

        // Customer statistics
        $totalCustomers = Customer::count();
        $activeCustomers = Customer::where('is_active', true)->count();
        $membershipStats = Customer::selectRaw('membership_type, COUNT(*) as count')
            ->groupBy('membership_type')
            ->get();

        // Monthly revenue chart data
        $monthlyRevenueChart = [];
        for ($i = 11; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $revenue = Booking::whereYear('start_time', $month->year)
                ->whereMonth('start_time', $month->month)
                ->where('payment_status', 'paid')
                ->sum('total_cost');
            $monthlyRevenueChart[] = [
                'month' => $month->format('M Y'),
                'revenue' => $revenue
            ];
        }

        return view('reports.index', compact(
            'todayRevenue', 'weeklyRevenue', 'monthlyRevenue', 'yearlyRevenue',
            'totalBookings', 'completedBookings', 'cancelledBookings', 'activeBookings',
            'consoleUtilization', 'popularGames', 'totalCustomers', 'activeCustomers',
            'membershipStats', 'monthlyRevenueChart'
        ));
    }

    /**
     * Revenue report
     */
    public function revenue(Request $request)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        $startDate = $request->input('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', now()->endOfMonth()->format('Y-m-d'));

        $bookings = Booking::with(['user', 'console'])
            ->whereBetween('start_time', [$startDate, $endDate])
            ->where('payment_status', 'paid')
            ->orderBy('start_time', 'desc')
            ->paginate(20);

        $totalRevenue = $bookings->sum('total_cost');
        $totalBookings = $bookings->count();
        $averageBookingValue = $totalBookings > 0 ? $totalRevenue / $totalBookings : 0;

        return view('reports.revenue', compact(
            'bookings', 'totalRevenue', 'totalBookings', 'averageBookingValue',
            'startDate', 'endDate'
        ));
    }

    /**
     * Console utilization report
     */
    public function consoleUtilization()
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        $consoles = Console::withCount([
            'bookings as total_bookings',
            'bookings as completed_bookings' => function ($query) {
                $query->where('status', 'completed');
            },
            'bookings as active_bookings' => function ($query) {
                $query->where('status', 'active');
            }
        ])
        ->with(['bookings' => function ($query) {
            $query->where('status', 'completed')
                  ->selectRaw('console_id, SUM(total_cost) as total_revenue, SUM(duration_hours) as total_hours')
                  ->groupBy('console_id');
        }])
        ->get();

        return view('reports.console-utilization', compact('consoles'));
    }

    /**
     * Customer report
     */
    public function customers()
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        $customers = Customer::with(['user', 'bookings'])
            ->withCount(['bookings as total_bookings'])
            ->orderBy('total_spent', 'desc')
            ->paginate(20);

        $topCustomers = Customer::orderBy('total_spent', 'desc')->take(10)->get();
        $membershipDistribution = Customer::selectRaw('membership_type, COUNT(*) as count')
            ->groupBy('membership_type')
            ->get();

        return view('reports.customers', compact('customers', 'topCustomers', 'membershipDistribution'));
    }

    /**
     * Export revenue report to CSV
     */
    public function exportRevenue(Request $request)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        $startDate = $request->input('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', now()->endOfMonth()->format('Y-m-d'));

        $bookings = Booking::with(['user', 'console'])
            ->whereBetween('start_time', [$startDate, $endDate])
            ->where('payment_status', 'paid')
            ->orderBy('start_time', 'desc')
            ->get();

        $filename = 'revenue_report_' . $startDate . '_to_' . $endDate . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($bookings) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Booking Code', 'Customer', 'Console', 'Start Time', 'End Time',
                'Duration (Hours)', 'Console Cost', 'Games Cost', 'Total Cost', 'Status'
            ]);

            // CSV data
            foreach ($bookings as $booking) {
                fputcsv($file, [
                    $booking->booking_code,
                    $booking->user->name ?? 'Unknown',
                    $booking->console->name ?? 'Unknown',
                    $booking->start_time->format('Y-m-d H:i'),
                    $booking->end_time->format('Y-m-d H:i'),
                    $booking->duration_hours,
                    $booking->console_cost,
                    $booking->games_cost,
                    $booking->total_cost,
                    $booking->status
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
