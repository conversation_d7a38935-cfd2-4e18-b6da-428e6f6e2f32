<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Console;

class ConsoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $consoles = [
            [
                'name' => 'PlayStation 5 #001',
                'type' => 'PlayStation 5',
                'description' => 'PlayStation 5 dengan SSD ultra-cepat, ray tracing, dan audio 3D. Dilengkapi dengan DualSense controller yang revolusioner.',
                'image' => 'ps5-console.jpg',
                'hourly_rate' => 25000,
                'daily_rate' => 180000,
                'status' => 'available',
                'serial_number' => 'PS5-001-2024',
                'specifications' => [
                    'storage' => '825GB SSD',
                    'cpu' => 'AMD Zen 2',
                    'gpu' => 'AMD RDNA 2',
                    'ram' => '16GB GDDR6',
                    'resolution' => '4K UHD'
                ],
                'controller_count' => 2,
            ],
            [
                'name' => 'PlayStation 5 #002',
                'type' => 'PlayStation 5',
                'description' => 'PlayStation 5 premium dengan performa gaming terdepan. Mendukung 4K gaming dan ray tracing real-time.',
                'image' => 'ps5-console.jpg',
                'hourly_rate' => 25000,
                'daily_rate' => 180000,
                'status' => 'available',
                'serial_number' => 'PS5-002-2024',
                'specifications' => [
                    'storage' => '825GB SSD',
                    'cpu' => 'AMD Zen 2',
                    'gpu' => 'AMD RDNA 2',
                    'ram' => '16GB GDDR6',
                    'resolution' => '4K UHD'
                ],
                'controller_count' => 2,
            ],
            [
                'name' => 'PlayStation 4 Pro #001',
                'type' => 'PlayStation 4 Pro',
                'description' => 'PlayStation 4 Pro dengan dukungan 4K dan HDR. Perpustakaan game yang luas dan performa yang handal.',
                'image' => 'ps4-pro-console.jpg',
                'hourly_rate' => 20000,
                'daily_rate' => 140000,
                'status' => 'available',
                'serial_number' => 'PS4P-001-2024',
                'specifications' => [
                    'storage' => '1TB HDD',
                    'cpu' => 'AMD Jaguar',
                    'gpu' => 'AMD Radeon',
                    'ram' => '8GB GDDR5',
                    'resolution' => '4K HDR'
                ],
                'controller_count' => 2,
            ],
            [
                'name' => 'PlayStation 4 Pro #002',
                'type' => 'PlayStation 4 Pro',
                'description' => 'PlayStation 4 Pro dengan koleksi game eksklusif terbaik. Cocok untuk gaming marathon.',
                'image' => 'ps4-pro-console.jpg',
                'hourly_rate' => 20000,
                'daily_rate' => 140000,
                'status' => 'rented',
                'serial_number' => 'PS4P-002-2024',
                'specifications' => [
                    'storage' => '1TB HDD',
                    'cpu' => 'AMD Jaguar',
                    'gpu' => 'AMD Radeon',
                    'ram' => '8GB GDDR5',
                    'resolution' => '4K HDR'
                ],
                'controller_count' => 2,
            ],
            [
                'name' => 'PlayStation 4 Slim #001',
                'type' => 'PlayStation 4 Slim',
                'description' => 'PlayStation 4 Slim yang compact dan hemat energi. Pilihan ekonomis untuk gaming berkualitas.',
                'image' => 'ps4-slim-console.jpg',
                'hourly_rate' => 15000,
                'daily_rate' => 100000,
                'status' => 'available',
                'serial_number' => 'PS4S-001-2024',
                'specifications' => [
                    'storage' => '500GB HDD',
                    'cpu' => 'AMD Jaguar',
                    'gpu' => 'AMD Radeon',
                    'ram' => '8GB GDDR5',
                    'resolution' => '1080p Full HD'
                ],
                'controller_count' => 2,
            ],
            [
                'name' => 'PlayStation 4 Slim #002',
                'type' => 'PlayStation 4 Slim',
                'description' => 'PlayStation 4 Slim dengan game library lengkap. Ideal untuk pemain casual dan keluarga.',
                'image' => 'ps4-slim-console.jpg',
                'hourly_rate' => 15000,
                'daily_rate' => 100000,
                'status' => 'maintenance',
                'serial_number' => 'PS4S-002-2024',
                'specifications' => [
                    'storage' => '500GB HDD',
                    'cpu' => 'AMD Jaguar',
                    'gpu' => 'AMD Radeon',
                    'ram' => '8GB GDDR5',
                    'resolution' => '1080p Full HD'
                ],
                'controller_count' => 2,
            ],
        ];

        foreach ($consoles as $console) {
            Console::create($console);
        }
    }
}
