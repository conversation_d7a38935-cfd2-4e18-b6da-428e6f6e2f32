<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Post;
use App\Models\User;

class PostSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();

        if ($users->count() > 0) {
            // Create sample posts
            $posts = [
                [
                    'title' => 'Welcome to Our Platform',
                    'content' => 'This is a welcome post to introduce you to our amazing platform. Here you can create, edit, and manage your posts with ease. The platform supports role-based access control, ensuring that admins and users have appropriate permissions.',
                    'user_id' => $users->where('role', 'admin')->first()->id ?? $users->first()->id,
                ],
                [
                    'title' => 'Getting Started Guide',
                    'content' => 'Here\'s a comprehensive guide to help you get started with the platform. First, make sure you\'re logged in. Then, navigate to the posts section where you can view all your posts. Click on "New Post" to create your first post. Remember to give it a descriptive title and engaging content.',
                    'user_id' => $users->where('role', 'admin')->first()->id ?? $users->first()->id,
                ],
                [
                    'title' => 'My First Post',
                    'content' => 'This is my very first post on this platform! I\'m excited to share my thoughts and ideas with everyone. The interface is really user-friendly and I love how easy it is to create and format posts.',
                    'user_id' => $users->where('role', 'user')->first()->id ?? $users->last()->id,
                ],
                [
                    'title' => 'Tips for Better Writing',
                    'content' => 'Here are some tips for writing better posts: 1. Start with a compelling title, 2. Structure your content with clear paragraphs, 3. Use examples to illustrate your points, 4. Proofread before publishing, 5. Engage with your readers through comments.',
                    'user_id' => $users->where('role', 'user')->skip(1)->first()->id ?? $users->last()->id,
                ],
                [
                    'title' => 'Platform Features Overview',
                    'content' => 'Our platform offers many great features including: Role-based access control, Responsive design that works on all devices, Easy post management with CRUD operations, Clean and modern UI with Tailwind CSS, Secure authentication system, and much more!',
                    'user_id' => $users->where('role', 'admin')->first()->id ?? $users->first()->id,
                ],
            ];

            foreach ($posts as $postData) {
                Post::create($postData);
            }
        }
    }
}
