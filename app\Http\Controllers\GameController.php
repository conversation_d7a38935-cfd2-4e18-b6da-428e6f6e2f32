<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Game;

class GameController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $games = Game::latest()->paginate(12);

        return view('games.index', compact('games'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        return view('games.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'genre' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|string|max:255',
            'platform' => 'required|in:PS4,PS5,Both',
            'rating' => 'required|in:E,T,M,AO',
            'release_year' => 'required|integer|min:1970|max:' . (date('Y') + 2),
            'rental_price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:1',
            'is_popular' => 'boolean',
            'rating_score' => 'nullable|numeric|min:0|max:10',
        ]);

        $gameData = $request->all();
        $gameData['available_stock'] = $gameData['stock'];
        $gameData['is_popular'] = $request->has('is_popular');

        Game::create($gameData);

        return redirect()->route('admin.games.index')
            ->with('success', 'Game created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Game $game)
    {
        return view('games.show', compact('game'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Game $game)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        return view('games.edit', compact('game'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Game $game)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'genre' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|string|max:255',
            'platform' => 'required|in:PS4,PS5,Both',
            'rating' => 'required|in:E,T,M,AO',
            'release_year' => 'required|integer|min:1970|max:' . (date('Y') + 2),
            'rental_price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:1',
            'available_stock' => 'required|integer|min:0|max:' . $request->stock,
            'is_popular' => 'boolean',
            'rating_score' => 'nullable|numeric|min:0|max:10',
        ]);

        $gameData = $request->all();
        $gameData['is_popular'] = $request->has('is_popular');

        $game->update($gameData);

        return redirect()->route('admin.games.show', $game)
            ->with('success', 'Game updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Game $game)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        $game->delete();

        return redirect()->route('admin.games.index')
            ->with('success', 'Game deleted successfully!');
    }

    /**
     * Filter games by platform
     */
    public function filterByPlatform(Request $request)
    {
        $platform = $request->input('platform');
        $genre = $request->input('genre');

        $query = Game::query();

        if ($platform && $platform !== 'all') {
            $query->forPlatform($platform);
        }

        if ($genre && $genre !== 'all') {
            $query->byGenre($genre);
        }

        $games = $query->latest()->paginate(12);
        $genres = Game::distinct()->pluck('genre');

        return view('games.index', compact('games', 'genres'));
    }
}
