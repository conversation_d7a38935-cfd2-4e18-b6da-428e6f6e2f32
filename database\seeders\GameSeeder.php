<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Game;

class GameSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $games = [
            // PS5 Exclusives
            [
                'title' => 'Spider-Man: <PERSON>',
                'genre' => 'Action/Adventure',
                'description' => 'Rasakan kekuatan Spider-Man baru dalam petualangan yang mendebarkan di New York City.',
                'image' => 'spiderman-miles-morales.jpg',
                'platform' => 'PS5',
                'rating' => 'T',
                'release_year' => 2020,
                'rental_price' => 5000,
                'stock' => 3,
                'available_stock' => 3,
                'is_popular' => true,
                'rating_score' => 9.2,
            ],
            [
                'title' => 'Demon\'s Souls',
                'genre' => 'Action RPG',
                'description' => 'Remake yang memukau dari game souls-like klasik dengan grafis next-gen.',
                'image' => 'demons-souls.jpg',
                'platform' => 'PS5',
                'rating' => 'M',
                'release_year' => 2020,
                'rental_price' => 5000,
                'stock' => 2,
                'available_stock' => 2,
                'is_popular' => true,
                'rating_score' => 9.0,
            ],
            [
                'title' => 'Ratchet & Clank: Rift Apart',
                'genre' => 'Action/Platformer',
                'description' => 'Petualangan interdimensional yang menampilkan kekuatan SSD PS5.',
                'image' => 'ratchet-clank-rift-apart.jpg',
                'platform' => 'PS5',
                'rating' => 'T',
                'release_year' => 2021,
                'rental_price' => 5000,
                'stock' => 2,
                'available_stock' => 1,
                'is_popular' => true,
                'rating_score' => 9.1,
            ],

            // PS4/PS5 Cross-gen
            [
                'title' => 'God of War',
                'genre' => 'Action/Adventure',
                'description' => 'Kratos dan Atreus dalam petualangan epik di dunia mitologi Norse.',
                'image' => 'god-of-war.jpg',
                'platform' => 'Both',
                'rating' => 'M',
                'release_year' => 2018,
                'rental_price' => 3000,
                'stock' => 4,
                'available_stock' => 4,
                'is_popular' => true,
                'rating_score' => 9.5,
            ],
            [
                'title' => 'The Last of Us Part II',
                'genre' => 'Action/Survival',
                'description' => 'Kelanjutan kisah Ellie dalam dunia post-apocalyptic yang brutal.',
                'image' => 'the-last-of-us-part-2.jpg',
                'platform' => 'Both',
                'rating' => 'M',
                'release_year' => 2020,
                'rental_price' => 4000,
                'stock' => 3,
                'available_stock' => 2,
                'is_popular' => true,
                'rating_score' => 9.3,
            ],
            [
                'title' => 'Ghost of Tsushima',
                'genre' => 'Action/Adventure',
                'description' => 'Jadilah samurai terakhir dalam perjuangan melawan invasi Mongol.',
                'image' => 'ghost-of-tsushima.jpg',
                'platform' => 'Both',
                'rating' => 'M',
                'release_year' => 2020,
                'rental_price' => 4000,
                'stock' => 3,
                'available_stock' => 3,
                'is_popular' => true,
                'rating_score' => 9.0,
            ],

            // Popular Multi-platform
            [
                'title' => 'FIFA 24',
                'genre' => 'Sports',
                'description' => 'Simulasi sepak bola terdepan dengan HyperMotion Technology.',
                'image' => 'fifa-24.jpg',
                'platform' => 'Both',
                'rating' => 'E',
                'release_year' => 2023,
                'rental_price' => 3000,
                'stock' => 5,
                'available_stock' => 4,
                'is_popular' => true,
                'rating_score' => 8.5,
            ],
            [
                'title' => 'Call of Duty: Modern Warfare III',
                'genre' => 'FPS',
                'description' => 'Aksi militer terbaru dengan campaign dan multiplayer yang intens.',
                'image' => 'cod-mw3.jpg',
                'platform' => 'Both',
                'rating' => 'M',
                'release_year' => 2023,
                'rental_price' => 4000,
                'stock' => 4,
                'available_stock' => 3,
                'is_popular' => true,
                'rating_score' => 8.7,
            ],
            [
                'title' => 'Grand Theft Auto V',
                'genre' => 'Action/Adventure',
                'description' => 'Open world crime saga yang legendaris di Los Santos.',
                'image' => 'gta-v.jpg',
                'platform' => 'Both',
                'rating' => 'M',
                'release_year' => 2013,
                'rental_price' => 2000,
                'stock' => 6,
                'available_stock' => 5,
                'is_popular' => true,
                'rating_score' => 9.7,
            ],
            [
                'title' => 'Minecraft',
                'genre' => 'Sandbox',
                'description' => 'Game kreatif tanpa batas untuk membangun dunia impian Anda.',
                'image' => 'minecraft.jpg',
                'platform' => 'Both',
                'rating' => 'E',
                'release_year' => 2011,
                'rental_price' => 2000,
                'stock' => 4,
                'available_stock' => 4,
                'is_popular' => true,
                'rating_score' => 9.0,
            ],

            // Family Games
            [
                'title' => 'Crash Bandicoot 4: It\'s About Time',
                'genre' => 'Platformer',
                'description' => 'Petualangan platformer klasik dengan twist modern.',
                'image' => 'crash-bandicoot-4.jpg',
                'platform' => 'Both',
                'rating' => 'E',
                'release_year' => 2020,
                'rental_price' => 3000,
                'stock' => 2,
                'available_stock' => 2,
                'is_popular' => false,
                'rating_score' => 8.5,
            ],
            [
                'title' => 'Sackboy: A Big Adventure',
                'genre' => 'Platformer',
                'description' => 'Petualangan co-op yang menyenangkan untuk seluruh keluarga.',
                'image' => 'sackboy-big-adventure.jpg',
                'platform' => 'Both',
                'rating' => 'E',
                'release_year' => 2020,
                'rental_price' => 3000,
                'stock' => 2,
                'available_stock' => 2,
                'is_popular' => false,
                'rating_score' => 8.8,
            ],
        ];

        foreach ($games as $game) {
            Game::create($game);
        }
    }
}
