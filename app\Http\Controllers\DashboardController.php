<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Post;
use App\Models\User;
use App\Models\Console;
use App\Models\Game;
use App\Models\Booking;
use App\Models\Customer;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Show admin dashboard
     */
    public function adminDashboard()
    {
        // Basic stats
        $totalUsers = User::count();
        $totalPosts = Post::count();
        $recentPosts = Post::with('user')->latest()->take(5)->get();

        // Rental stats
        $totalConsoles = Console::count();
        $availableConsoles = Console::where('status', 'available')->count();
        $rentedConsoles = Console::where('status', 'rented')->count();
        $maintenanceConsoles = Console::where('status', 'maintenance')->count();

        $totalGames = Game::count();
        $popularGames = Game::where('is_popular', true)->count();

        $totalBookings = Booking::count();
        $activeBookings = Booking::where('status', 'active')->count();
        $pendingBookings = Booking::where('status', 'pending')->count();
        $todayBookings = Booking::whereDate('start_time', today())->count();

        // Revenue stats
        $todayRevenue = Booking::whereDate('start_time', today())
            ->where('payment_status', 'paid')
            ->sum('total_cost');
        $monthlyRevenue = Booking::whereMonth('start_time', now()->month)
            ->where('payment_status', 'paid')
            ->sum('total_cost');
        $totalRevenue = Booking::where('payment_status', 'paid')->sum('total_cost');

        // Recent bookings
        $recentBookings = Booking::with(['user', 'console'])
            ->latest()
            ->take(5)
            ->get();

        // Console utilization
        $consoleStats = Console::withCount(['bookings as total_bookings'])
            ->get()
            ->map(function ($console) {
                $activeBooking = $console->currentBooking();
                return [
                    'console' => $console,
                    'active_booking' => $activeBooking,
                    'utilization' => $console->total_bookings,
                ];
            });

        return view('admin.dashboard', compact(
            'totalUsers', 'totalPosts', 'recentPosts',
            'totalConsoles', 'availableConsoles', 'rentedConsoles', 'maintenanceConsoles',
            'totalGames', 'popularGames',
            'totalBookings', 'activeBookings', 'pendingBookings', 'todayBookings',
            'todayRevenue', 'monthlyRevenue', 'totalRevenue',
            'recentBookings', 'consoleStats'
        ));
    }

    /**
     * Show user dashboard
     */
    public function userDashboard()
    {
        $user = auth()->user();
        $customer = $user->getOrCreateCustomer();

        // User stats
        $userPosts = Post::where('user_id', $user->id)->count();
        $recentPosts = Post::where('user_id', $user->id)->latest()->take(5)->get();

        // Rental stats
        $totalBookings = $user->bookings()->count();
        $activeBookings = $user->bookings()->where('status', 'active')->count();
        $upcomingBookings = $user->bookings()
            ->where('status', 'confirmed')
            ->where('start_time', '>', now())
            ->count();

        // Recent bookings
        $recentBookings = $user->bookings()
            ->with(['console'])
            ->latest()
            ->take(5)
            ->get();

        // Available consoles
        $availableConsoles = Console::where('status', 'available')->take(6)->get();

        // Popular games
        $popularGames = Game::where('is_popular', true)->take(8)->get();

        return view('user.dashboard', compact(
            'userPosts', 'recentPosts', 'customer',
            'totalBookings', 'activeBookings', 'upcomingBookings',
            'recentBookings', 'availableConsoles', 'popularGames'
        ));
    }
}
