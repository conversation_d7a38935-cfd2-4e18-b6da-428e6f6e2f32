<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Post;
use App\Models\User;

class DashboardController extends Controller
{
    /**
     * Show admin dashboard
     */
    public function adminDashboard()
    {
        $totalUsers = User::count();
        $totalPosts = Post::count();
        $recentPosts = Post::with('user')->latest()->take(5)->get();

        return view('admin.dashboard', compact('totalUsers', 'totalPosts', 'recentPosts'));
    }

    /**
     * Show user dashboard
     */
    public function userDashboard()
    {
        $user = auth()->user();
        $userPosts = Post::where('user_id', $user->id)->count();
        $recentPosts = Post::where('user_id', $user->id)->latest()->take(5)->get();

        return view('user.dashboard', compact('userPosts', 'recentPosts'));
    }
}
