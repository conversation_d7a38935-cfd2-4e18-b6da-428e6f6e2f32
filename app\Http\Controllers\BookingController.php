<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Booking;
use App\Models\Console;
use App\Models\Game;
use App\Models\Customer;
use Carbon\Carbon;

class BookingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = auth()->user();

        if ($user->isAdmin()) {
            $bookings = Booking::with(['user', 'console'])
                ->latest()
                ->paginate(15);
        } else {
            $bookings = $user->bookings()
                ->with(['console'])
                ->latest()
                ->paginate(15);
        }

        return view('bookings.index', compact('bookings'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $availableConsoles = Console::where('status', 'available')->get();
        $games = Game::where('available_stock', '>', 0)->get();

        return view('bookings.create', compact('availableConsoles', 'games'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'console_id' => 'required|exists:consoles,id',
            'start_time' => 'required|date|after:now',
            'duration_hours' => 'required|integer|min:1|max:24',
            'game_ids' => 'nullable|array',
            'game_ids.*' => 'exists:games,id',
        ]);

        $console = Console::findOrFail($request->console_id);

        // Check console availability
        if (!$console->isAvailable()) {
            return back()->withErrors(['console_id' => 'Console is not available.']);
        }

        $startTime = Carbon::parse($request->start_time);
        $endTime = $startTime->copy()->addHours($request->duration_hours);

        // Check for conflicting bookings
        $conflictingBooking = Booking::where('console_id', $console->id)
            ->where('status', '!=', 'cancelled')
            ->where(function ($query) use ($startTime, $endTime) {
                $query->whereBetween('start_time', [$startTime, $endTime])
                    ->orWhereBetween('end_time', [$startTime, $endTime])
                    ->orWhere(function ($q) use ($startTime, $endTime) {
                        $q->where('start_time', '<=', $startTime)
                          ->where('end_time', '>=', $endTime);
                    });
            })
            ->exists();

        if ($conflictingBooking) {
            return back()->withErrors(['start_time' => 'Console is already booked for this time period.']);
        }

        // Calculate costs
        $consoleCost = $console->hourly_rate * $request->duration_hours;
        $gamesCost = 0;

        if ($request->game_ids) {
            $selectedGames = Game::whereIn('id', $request->game_ids)->get();
            $gamesCost = $selectedGames->sum('rental_price') * $request->duration_hours;
        }

        $totalCost = $consoleCost + $gamesCost;

        // Apply membership discount if user has customer profile
        $discount = 0;
        if (auth()->user()->customer) {
            $customer = auth()->user()->customer;
            $discount = $customer->getMembershipDiscount();
            if ($discount > 0) {
                $totalCost = $totalCost * (1 - $discount / 100);
            }
        }

        // Create booking
        $booking = Booking::create([
            'booking_code' => Booking::generateBookingCode(),
            'user_id' => auth()->id(),
            'console_id' => $console->id,
            'game_ids' => $request->game_ids,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'duration_hours' => $request->duration_hours,
            'console_cost' => $consoleCost,
            'games_cost' => $gamesCost,
            'total_cost' => $totalCost,
            'status' => 'pending',
            'payment_status' => 'unpaid',
        ]);

        return redirect()->route(auth()->user()->isAdmin() ? 'admin.bookings.show' : 'user.bookings.show', $booking)
            ->with('success', 'Booking created successfully! Booking code: ' . $booking->booking_code);
    }

    /**
     * Display the specified resource.
     */
    public function show(Booking $booking)
    {
        $user = auth()->user();

        // Check if user can view this booking
        if (!$user->isAdmin() && $booking->user_id !== $user->id) {
            abort(403, 'Unauthorized access.');
        }

        $booking->load(['user', 'console']);
        $games = $booking->games();

        return view('bookings.show', compact('booking', 'games'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Booking $booking)
    {
        $user = auth()->user();

        // Check if user can edit this booking
        if (!$user->isAdmin() && $booking->user_id !== $user->id) {
            abort(403, 'Unauthorized access.');
        }

        // Only allow editing pending bookings
        if ($booking->status !== 'pending') {
            return back()->withErrors(['status' => 'Only pending bookings can be edited.']);
        }

        $availableConsoles = Console::where('status', 'available')
            ->orWhere('id', $booking->console_id)
            ->get();
        $games = Game::where('available_stock', '>', 0)->get();

        return view('bookings.edit', compact('booking', 'availableConsoles', 'games'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Booking $booking)
    {
        $user = auth()->user();

        // Check if user can update this booking
        if (!$user->isAdmin() && $booking->user_id !== $user->id) {
            abort(403, 'Unauthorized access.');
        }

        // Only allow updating pending bookings
        if ($booking->status !== 'pending') {
            return back()->withErrors(['status' => 'Only pending bookings can be updated.']);
        }

        $request->validate([
            'console_id' => 'required|exists:consoles,id',
            'start_time' => 'required|date|after:now',
            'duration_hours' => 'required|integer|min:1|max:24',
            'game_ids' => 'nullable|array',
            'game_ids.*' => 'exists:games,id',
        ]);

        // Similar validation and calculation logic as store method
        // ... (implementation details)

        $booking->update([
            'console_id' => $request->console_id,
            'start_time' => $request->start_time,
            'duration_hours' => $request->duration_hours,
            'game_ids' => $request->game_ids,
            // ... other fields
        ]);

        return redirect()->route(auth()->user()->isAdmin() ? 'admin.bookings.show' : 'user.bookings.show', $booking)
            ->with('success', 'Booking updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Booking $booking)
    {
        $user = auth()->user();

        // Check if user can delete this booking
        if (!$user->isAdmin() && $booking->user_id !== $user->id) {
            abort(403, 'Unauthorized access.');
        }

        // Only allow cancelling pending or confirmed bookings
        if (!in_array($booking->status, ['pending', 'confirmed'])) {
            return back()->withErrors(['status' => 'This booking cannot be cancelled.']);
        }

        $booking->update(['status' => 'cancelled']);

        return redirect()->route(auth()->user()->isAdmin() ? 'admin.bookings.index' : 'user.bookings.index')
            ->with('success', 'Booking cancelled successfully!');
    }

    /**
     * Confirm a booking (Admin only)
     */
    public function confirm(Booking $booking)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        if ($booking->status !== 'pending') {
            return back()->withErrors(['status' => 'Only pending bookings can be confirmed.']);
        }

        $booking->update(['status' => 'confirmed']);

        return back()->with('success', 'Booking confirmed successfully!');
    }

    /**
     * Check in a booking (Admin only)
     */
    public function checkIn(Booking $booking)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        if (!$booking->canCheckIn()) {
            return back()->withErrors(['status' => 'This booking cannot be checked in.']);
        }

        $booking->update([
            'status' => 'active',
            'checked_in_at' => now(),
        ]);

        // Update console status
        $booking->console->update(['status' => 'rented']);

        return back()->with('success', 'Customer checked in successfully!');
    }

    /**
     * Check out a booking (Admin only)
     */
    public function checkOut(Booking $booking)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        if (!$booking->canCheckOut()) {
            return back()->withErrors(['status' => 'This booking cannot be checked out.']);
        }

        $booking->update([
            'status' => 'completed',
            'checked_out_at' => now(),
        ]);

        // Update console status
        $booking->console->update(['status' => 'available']);

        // Update customer stats
        $customer = $booking->user->getOrCreateCustomer();
        $customer->updateStats($booking->total_cost);

        return back()->with('success', 'Customer checked out successfully!');
    }
}
