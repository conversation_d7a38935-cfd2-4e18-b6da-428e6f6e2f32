<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Gaming Header -->
    <div class="bg-gradient-to-r from-purple-900 via-pink-900 to-red-900 rounded-2xl shadow-xl p-8 text-white relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full floating"></div>
            <div class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full floating" style="animation-delay: 1s;"></div>
        </div>
        
        <div class="relative z-10 flex items-center justify-between">
            <div>
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-4 pulse-glow">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M7.5,4A5.5,5.5 0 0,0 2,9.5C2,10 2.04,10.5 2.12,11H6.5L7.66,8.5H8.34L9.5,11H13.88C13.96,10.5 14,10 14,9.5A5.5,5.5 0 0,0 8.5,4H7.5M21.8,12H2.2L1.27,21H22.73L21.8,12Z"/>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold gaming-font neon-text"><?php echo e($game->title); ?></h1>
                        <p class="text-purple-100 text-lg"><?php echo e($game->genre); ?> • <?php echo e($game->platform); ?></p>
                    </div>
                </div>
            </div>
            <div class="text-right">
                <?php if($game->is_popular): ?>
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium bg-yellow-100 text-yellow-800 pulse-glow">
                        ⭐ Popular
                    </span>
                <?php endif; ?>
                <div class="mt-2">
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium bg-purple-100 text-purple-800">
                        <?php echo e($game->rating); ?>

                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Game Details -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Game Information -->
        <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
            <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
                <svg class="w-6 h-6 mr-2 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M11,15H13V17H11V15M11,7H13V13H11V7M12,2C6.47,2 2,6.5 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                </svg>
                Game Information
            </h2>
            
            <!-- Basic Info -->
            <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-6 mb-6 border border-purple-200">
                <div class="text-center mb-6">
                    <div class="w-24 h-24 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4 floating">
                        <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M7.5,4A5.5,5.5 0 0,0 2,9.5C2,10 2.04,10.5 2.12,11H6.5L7.66,8.5H8.34L9.5,11H13.88C13.96,10.5 14,10 14,9.5A5.5,5.5 0 0,0 8.5,4H7.5M21.8,12H2.2L1.27,21H22.73L21.8,12Z"/>
                        </svg>
                    </div>
                    <h3 class="font-bold text-gray-900 text-xl"><?php echo e($game->title); ?></h3>
                    <p class="text-gray-600"><?php echo e($game->genre); ?></p>
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-3 bg-white rounded-lg border border-purple-100">
                        <p class="text-sm text-gray-600 mb-1">Platform</p>
                        <p class="font-bold text-purple-600"><?php echo e($game->platform); ?></p>
                    </div>
                    <div class="text-center p-3 bg-white rounded-lg border border-purple-100">
                        <p class="text-sm text-gray-600 mb-1">Rating</p>
                        <p class="font-bold text-purple-600"><?php echo e($game->rating); ?></p>
                    </div>
                    <div class="text-center p-3 bg-white rounded-lg border border-purple-100">
                        <p class="text-sm text-gray-600 mb-1">Release Year</p>
                        <p class="font-bold text-purple-600"><?php echo e($game->release_year); ?></p>
                    </div>
                    <div class="text-center p-3 bg-white rounded-lg border border-purple-100">
                        <p class="text-sm text-gray-600 mb-1">Stock</p>
                        <p class="font-bold text-purple-600"><?php echo e($game->available_stock); ?>/<?php echo e($game->stock); ?></p>
                    </div>
                </div>
            </div>
            
            <!-- Rating & Reviews -->
            <?php if($game->rating_score): ?>
                <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-6 border border-yellow-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                        Player Rating
                    </h3>
                    
                    <div class="flex items-center justify-center">
                        <div class="text-center">
                            <div class="text-4xl font-bold text-yellow-600 neon-text mb-2"><?php echo e($game->rating_score); ?>/10</div>
                            <div class="flex items-center justify-center space-x-1 mb-2">
                                <?php for($i = 1; $i <= 5; $i++): ?>
                                    <?php if($i <= ($game->rating_score / 2)): ?>
                                        <svg class="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                    <?php else: ?>
                                        <svg class="w-6 h-6 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                    <?php endif; ?>
                                <?php endfor; ?>
                            </div>
                            <p class="text-sm text-gray-600">Excellent Game!</p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Pricing & Actions -->
        <div class="space-y-6">
            <!-- Pricing -->
            <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
                <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
                    <svg class="w-6 h-6 mr-2 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z"/>
                    </svg>
                    Rental Pricing
                </h2>
                
                <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 border border-green-200">
                    <div class="text-center">
                        <div class="text-4xl font-bold text-green-600 neon-text mb-2">
                            +Rp <?php echo e(number_format($game->rental_price)); ?>

                        </div>
                        <p class="text-gray-600 mb-4">per hour (additional to console rental)</p>
                        
                        <!-- Sample calculations -->
                        <div class="bg-white rounded-lg p-4 border border-green-100">
                            <h4 class="font-semibold text-gray-900 mb-3">Sample Pricing:</h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">3 hours gaming:</span>
                                    <span class="font-medium">+Rp <?php echo e(number_format($game->rental_price * 3)); ?></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">6 hours gaming:</span>
                                    <span class="font-medium">+Rp <?php echo e(number_format($game->rental_price * 6)); ?></span>
                                </div>
                                <div class="flex justify-between border-t border-gray-200 pt-2">
                                    <span class="text-gray-600">Full day (8 hours):</span>
                                    <span class="font-bold text-green-600">+Rp <?php echo e(number_format($game->rental_price * 8)); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
                <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
                    <svg class="w-6 h-6 mr-2 text-orange-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21V20A2,2 0 0,1 19,22H5A2,2 0 0,1 3,20V19H2A1,1 0 0,1 1,18V15A1,1 0 0,1 2,14H3A7,7 0 0,1 10,7H11V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2M7.5,13A2.5,2.5 0 0,0 5,15.5A2.5,2.5 0 0,0 7.5,18A2.5,2.5 0 0,0 10,15.5A2.5,2.5 0 0,0 7.5,13M16.5,13A2.5,2.5 0 0,0 14,15.5A2.5,2.5 0 0,0 16.5,18A2.5,2.5 0 0,0 19,15.5A2.5,2.5 0 0,0 16.5,13Z"/>
                    </svg>
                    Actions
                </h2>
                
                <div class="space-y-4">
                    <?php if(!auth()->user()->isAdmin() && $game->available_stock > 0): ?>
                        <a href="<?php echo e(route('user.bookings.create', ['game_id' => $game->id])); ?>" class="block w-full bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-semibold text-center transition-all duration-200 transform hover:scale-105 pulse-glow">
                            🎮 Book with This Game
                        </a>
                    <?php elseif(!auth()->user()->isAdmin()): ?>
                        <div class="block w-full bg-gray-300 text-gray-500 px-6 py-3 rounded-lg font-semibold text-center">
                            📦 Out of Stock
                        </div>
                    <?php endif; ?>
                    
                    <?php if(auth()->user()->isAdmin()): ?>
                        <a href="<?php echo e(route('admin.games.edit', $game)); ?>" class="block w-full bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white px-6 py-3 rounded-lg font-semibold text-center transition-all duration-200 transform hover:scale-105">
                            ✏️ Edit Game
                        </a>
                        
                        <!-- Stock Management -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="font-semibold text-gray-900 mb-3">Stock Management:</h3>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600">Available Stock:</span>
                                <span class="font-bold text-purple-600"><?php echo e($game->available_stock); ?>/<?php echo e($game->stock); ?></span>
                            </div>
                            <?php if($game->available_stock < $game->stock): ?>
                                <div class="mt-2 text-sm text-yellow-600">
                                    <?php echo e($game->stock - $game->available_stock); ?> copies currently rented
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Back Button -->
                    <a href="<?php echo e(route(auth()->user()->isAdmin() ? 'admin.games.index' : 'user.games.index')); ?>" class="block w-full bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-6 py-3 rounded-lg font-semibold text-center transition-all duration-200">
                        ← Back to Games
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Game Description -->
    <?php if($game->description): ?>
        <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
            <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
                <svg class="w-6 h-6 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                </svg>
                Game Description
            </h2>
            
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                <p class="text-gray-700 leading-relaxed"><?php echo e($game->description); ?></p>
            </div>
        </div>
    <?php endif; ?>

    <!-- Compatible Consoles -->
    <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
        <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
            <svg class="w-6 h-6 mr-2 text-indigo-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M21,6H3A1,1 0 0,0 2,7V17A1,1 0 0,0 3,18H21A1,1 0 0,0 22,17V7A1,1 0 0,0 21,6M20,16H4V8H20V16Z"/>
            </svg>
            Compatible Consoles
        </h2>
        
        <div class="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-200">
            <div class="text-center">
                <?php if($game->platform === 'PS5'): ?>
                    <div class="inline-flex items-center px-6 py-3 bg-blue-100 text-blue-800 rounded-full text-lg font-semibold">
                        🎮 PlayStation 5 Only
                    </div>
                    <p class="text-gray-600 mt-3">This game requires a PlayStation 5 console</p>
                <?php elseif($game->platform === 'PS4'): ?>
                    <div class="inline-flex items-center px-6 py-3 bg-green-100 text-green-800 rounded-full text-lg font-semibold">
                        🎮 PlayStation 4 Only
                    </div>
                    <p class="text-gray-600 mt-3">This game works on PlayStation 4 consoles</p>
                <?php else: ?>
                    <div class="space-x-4">
                        <div class="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full font-semibold">
                            🎮 PlayStation 5
                        </div>
                        <div class="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full font-semibold">
                            🎮 PlayStation 4
                        </div>
                    </div>
                    <p class="text-gray-600 mt-3">This game is compatible with both PlayStation 4 and PlayStation 5</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\TokoRentalPlayStation\resources\views/games/show.blade.php ENDPATH**/ ?>