<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <title>🎮 PlayStation Rental - Gaming Paradise</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=orbitron:400,500,600,700,800,900&family=exo-2:400,500,600,700&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

        <!-- Heroicons -->
        <script src="https://unpkg.com/@heroicons/react@2.0.18/24/outline/index.js" type="module"></script>

        <style>
            .gaming-font { font-family: 'Orbitron', monospace; }
            .content-font { font-family: 'Exo 2', sans-serif; }
            .gaming-bg {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                background-attachment: fixed;
            }
            .playstation-gradient {
                background: linear-gradient(135deg, #003087 0%, #0070f3 50%, #00d4ff 100%);
            }
            .gaming-card {
                backdrop-filter: blur(10px);
                background: rgba(255, 255, 255, 0.95);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            .glow-effect {
                box-shadow: 0 0 30px rgba(0, 112, 243, 0.3);
            }
            .pulse-glow {
                animation: pulse-glow 2s ease-in-out infinite alternate;
            }
            @keyframes pulse-glow {
                from { box-shadow: 0 0 20px rgba(0, 112, 243, 0.4); }
                to { box-shadow: 0 0 40px rgba(0, 112, 243, 0.8); }
            }
            .floating {
                animation: floating 3s ease-in-out infinite;
            }
            @keyframes floating {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-10px); }
            }
            .gaming-pattern {
                background-image:
                    radial-gradient(circle at 25% 25%, rgba(0, 112, 243, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
            }
            .neon-text {
                text-shadow: 0 0 10px rgba(0, 112, 243, 0.8), 0 0 20px rgba(0, 112, 243, 0.6), 0 0 30px rgba(0, 112, 243, 0.4);
            }
        </style>
    </head>
    <body class="content-font antialiased gaming-bg gaming-pattern">
        <div class="min-h-screen flex">
            <!-- Sidebar -->
            <?php echo $__env->make('layouts.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            <!-- Main Content -->
            <div class="flex-1 flex flex-col overflow-hidden">
                <!-- Top Navigation -->
                <?php echo $__env->make('layouts.topnav', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                <!-- Page Content -->
                <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50">
                    <!-- Flash Messages -->
                    <?php if(session('success')): ?>
                        <div class="mx-6 mt-6">
                            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                                <span class="block sm:inline"><?php echo e(session('success')); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="mx-6 mt-6">
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                                <span class="block sm:inline"><?php echo e(session('error')); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Page Heading -->
                    <?php if(isset($header)): ?>
                        <header class="bg-white shadow-sm border-b border-gray-200">
                            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                                <?php echo e($header); ?>

                            </div>
                        </header>
                    <?php endif; ?>

                    <!-- Main Content Area -->
                    <div class="container mx-auto px-6 py-8">
                        <?php echo $__env->yieldContent('content'); ?>
                        <?php echo e($slot ?? ''); ?>

                    </div>
                </main>
            </div>
        </div>

        <!-- Mobile menu overlay -->
        <div id="mobile-menu-overlay" class="fixed inset-0 z-40 bg-black bg-opacity-50 hidden lg:hidden"></div>

        <!-- Notification Dropdown (for desktop) -->
        <?php if(auth()->user()->isAdmin()): ?>
            <div id="notification-dropdown"
                 class="hidden fixed top-16 right-4 w-80 bg-white rounded-lg shadow-xl border border-gray-200 z-50 gaming-card glow-effect">
                <div class="p-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900 gaming-font">🔔 Notifications</h3>
                        <a href="<?php echo e(route('admin.notifications.index')); ?>"
                           class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            View All
                        </a>
                    </div>
                </div>
                <div id="notification-list" class="max-h-96 overflow-y-auto">
                    <div class="p-4 text-center text-gray-500">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
                        Loading notifications...
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <script>
            // Mobile menu toggle
            document.addEventListener('DOMContentLoaded', function() {
                const mobileMenuButton = document.getElementById('mobile-menu-button');
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('mobile-menu-overlay');

                if (mobileMenuButton) {
                    mobileMenuButton.addEventListener('click', function() {
                        sidebar.classList.toggle('-translate-x-full');
                        overlay.classList.toggle('hidden');
                    });
                }

                if (overlay) {
                    overlay.addEventListener('click', function() {
                        sidebar.classList.add('-translate-x-full');
                        overlay.classList.add('hidden');
                    });
                }
            });

            // Notification Scripts
            <?php if(auth()->user()->isAdmin()): ?>
                let notificationDropdownOpen = false;

                // Load notifications on page load
                loadNotifications();
                // Auto-refresh every 30 seconds
                setInterval(loadNotifications, 30000);

                function toggleNotifications() {
                    const dropdown = document.getElementById('notification-dropdown');
                    notificationDropdownOpen = !notificationDropdownOpen;

                    if (notificationDropdownOpen) {
                        dropdown.classList.remove('hidden');
                        loadNotifications();
                    } else {
                        dropdown.classList.add('hidden');
                    }
                }

                function loadNotifications() {
                    fetch('/admin/notifications/api')
                        .then(response => response.json())
                        .then(data => {
                            updateNotificationBadge(data.unread_count);
                            updateNotificationList(data.notifications);
                        })
                        .catch(error => {
                            console.error('Error loading notifications:', error);
                        });
                }

                function updateNotificationBadge(count) {
                    const badge = document.getElementById('notification-badge');
                    if (badge) {
                        badge.textContent = count;
                        if (count > 0) {
                            badge.classList.remove('hidden');
                        } else {
                            badge.classList.add('hidden');
                        }
                    }
                }

                function updateNotificationList(notifications) {
                    const list = document.getElementById('notification-list');
                    if (!list) return;

                    if (notifications.length === 0) {
                        list.innerHTML = `
                            <div class="p-4 text-center text-gray-500">
                                <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,15H13V17H11V15M11,7H13V13H11V7Z"/>
                                </svg>
                                <p class="text-sm">No new notifications</p>
                            </div>
                        `;
                        return;
                    }

                    const html = notifications.slice(0, 5).map(notification => {
                        const priorityColors = {
                            urgent: 'bg-red-100 text-red-800',
                            high: 'bg-orange-100 text-orange-800',
                            medium: 'bg-yellow-100 text-yellow-800',
                            low: 'bg-blue-100 text-blue-800'
                        };

                        const priorityIcons = {
                            urgent: '🚨',
                            high: '⚠️',
                            medium: '📋',
                            low: 'ℹ️'
                        };

                        return `
                            <div class="p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-200 ${!notification.read ? 'bg-blue-50' : ''}">
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0">
                                        <span class="text-lg">${priorityIcons[notification.priority]}</span>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center justify-between mb-1">
                                            <p class="text-sm font-semibold text-gray-900 truncate">${notification.title}</p>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${priorityColors[notification.priority]}">
                                                ${notification.priority}
                                            </span>
                                        </div>
                                        <p class="text-xs text-gray-600 mb-2 line-clamp-2">${notification.message}</p>
                                        <div class="flex items-center justify-between">
                                            <span class="text-xs text-gray-500">${formatTime(notification.time)}</span>
                                            ${notification.booking_id ? `<a href="/admin/bookings/${notification.booking_id}" class="text-xs text-blue-600 hover:text-blue-800">View →</a>` : ''}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    }).join('');

                    list.innerHTML = html;
                }

                function formatTime(timeString) {
                    const time = new Date(timeString);
                    const now = new Date();
                    const diffInMinutes = Math.floor((now - time) / (1000 * 60));

                    if (diffInMinutes < 1) return 'Just now';
                    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
                    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
                    return `${Math.floor(diffInMinutes / 1440)}d ago`;
                }

                // Close dropdown when clicking outside
                document.addEventListener('click', function(event) {
                    const dropdown = document.getElementById('notification-dropdown');
                    const button = event.target.closest('[onclick="toggleNotifications()"]');

                    if (!button && !dropdown.contains(event.target) && notificationDropdownOpen) {
                        dropdown.classList.add('hidden');
                        notificationDropdownOpen = false;
                    }
                });
            <?php endif; ?>
        </script>
    </body>
</html>
<?php /**PATH C:\Users\<USER>\TokoRentalPlayStation\resources\views/layouts/app.blade.php ENDPATH**/ ?>