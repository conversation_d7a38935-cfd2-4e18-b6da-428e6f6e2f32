@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Gaming Header -->
    <div class="bg-gradient-to-r from-green-900 via-teal-900 to-blue-900 rounded-2xl shadow-xl p-8 text-white relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full floating"></div>
            <div class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full floating" style="animation-delay: 1s;"></div>
        </div>
        
        <div class="relative z-10 flex items-center justify-between">
            <div>
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-4 pulse-glow">
                        <span class="text-white font-bold text-2xl gaming-font">
                            {{ strtoupper(substr($customer->user->name ?? 'U', 0, 1)) }}
                        </span>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold gaming-font neon-text">{{ $customer->user->name ?? 'Unknown Customer' }}</h1>
                        <p class="text-green-100 text-lg">{{ $customer->user->email ?? 'N/A' }}</p>
                    </div>
                </div>
            </div>
            <div class="text-right">
                @if($customer->is_active)
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium bg-green-100 text-green-800 pulse-glow">
                        ✅ Active Member
                    </span>
                @else
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium bg-red-100 text-red-800">
                        ❌ Inactive
                    </span>
                @endif
                <div class="mt-2">
                    @if($customer->membership_type === 'platinum')
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium bg-purple-100 text-purple-800">
                            💎 Platinum Member
                        </span>
                    @elseif($customer->membership_type === 'gold')
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium bg-yellow-100 text-yellow-800">
                            🥇 Gold Member
                        </span>
                    @elseif($customer->membership_type === 'silver')
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium bg-gray-100 text-gray-800">
                            🥈 Silver Member
                        </span>
                    @else
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium bg-blue-100 text-blue-800">
                            🎮 Regular Member
                        </span>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Personal Information -->
        <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
            <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
                <svg class="w-6 h-6 mr-2 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
                </svg>
                Personal Information
            </h2>
            
            <div class="space-y-4">
                <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-4 border border-green-200">
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-gray-600 font-medium">Full Name:</span>
                        <span class="font-bold text-gray-900">{{ $customer->user->name ?? 'N/A' }}</span>
                    </div>
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-gray-600 font-medium">Email:</span>
                        <span class="font-medium text-blue-600">{{ $customer->user->email ?? 'N/A' }}</span>
                    </div>
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-gray-600 font-medium">Phone:</span>
                        <span class="font-medium text-gray-900">{{ $customer->phone ?? 'Not provided' }}</span>
                    </div>
                    @if($customer->birth_date)
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-gray-600 font-medium">Birth Date:</span>
                            <span class="font-medium text-gray-900">{{ $customer->birth_date->format('d M Y') }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600 font-medium">Age:</span>
                            <span class="font-medium text-gray-900">{{ $customer->birth_date->age }} years old</span>
                        </div>
                    @endif
                </div>
                
                @if($customer->address)
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <h3 class="font-semibold text-gray-900 mb-2">Address:</h3>
                        <p class="text-gray-700">{{ $customer->address }}</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Membership & Statistics -->
        <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
            <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
                <svg class="w-6 h-6 mr-2 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M5,16L3,5H1V3H4L6,14H18L20,7H8L8.5,5H21A1,1 0 0,1 22,6A1,1 0 0,1 21.8,6.2L19.8,15.2C19.7,15.7 19.3,16 18.8,16H6.2C5.7,16 5.3,15.7 5.2,15.2L5,16M7,18A2,2 0 0,1 9,20A2,2 0 0,1 7,22A2,2 0 0,1 5,20A2,2 0 0,1 7,18M17,18A2,2 0 0,1 19,20A2,2 0 0,1 17,22A2,2 0 0,1 15,20A2,2 0 0,1 17,18Z"/>
                </svg>
                Membership & Statistics
            </h2>
            
            <!-- Membership Info -->
            <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-6 mb-6 border border-purple-200">
                <div class="text-center mb-4">
                    @if($customer->membership_type === 'platinum')
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-3 floating">
                            <span class="text-white text-2xl">💎</span>
                        </div>
                        <h3 class="text-xl font-bold text-purple-600">Platinum Member</h3>
                        <p class="text-gray-600">15% discount on all rentals</p>
                    @elseif($customer->membership_type === 'gold')
                        <div class="w-16 h-16 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-3 floating">
                            <span class="text-white text-2xl">🥇</span>
                        </div>
                        <h3 class="text-xl font-bold text-yellow-600">Gold Member</h3>
                        <p class="text-gray-600">10% discount on all rentals</p>
                    @elseif($customer->membership_type === 'silver')
                        <div class="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-full flex items-center justify-center mx-auto mb-3 floating">
                            <span class="text-white text-2xl">🥈</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-600">Silver Member</h3>
                        <p class="text-gray-600">5% discount on all rentals</p>
                    @else
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-3 floating">
                            <span class="text-white text-2xl">🎮</span>
                        </div>
                        <h3 class="text-xl font-bold text-blue-600">Regular Member</h3>
                        <p class="text-gray-600">Standard pricing</p>
                    @endif
                </div>
                
                @if($customer->membership_expires)
                    <div class="text-center">
                        <p class="text-sm text-gray-600">Membership expires:</p>
                        <p class="font-bold text-gray-900">{{ $customer->membership_expires->format('d M Y') }}</p>
                        @if($customer->membership_expires->isPast())
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-2">
                                Expired
                            </span>
                        @elseif($customer->membership_expires->diffInDays() <= 30)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mt-2">
                                Expires Soon
                            </span>
                        @else
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-2">
                                Active
                            </span>
                        @endif
                    </div>
                @endif
            </div>
            
            <!-- Statistics -->
            <div class="grid grid-cols-2 gap-4">
                <div class="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                    <p class="text-sm text-gray-600 mb-1">Total Spent</p>
                    <p class="text-2xl font-bold text-green-600 neon-text">Rp {{ number_format($customer->total_spent) }}</p>
                </div>
                <div class="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <p class="text-sm text-gray-600 mb-1">Total Bookings</p>
                    <p class="text-2xl font-bold text-blue-600 neon-text">{{ $customer->total_bookings ?? 0 }}</p>
                </div>
                <div class="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                    <p class="text-sm text-gray-600 mb-1">Member Since</p>
                    <p class="text-lg font-bold text-purple-600">{{ $customer->created_at->format('M Y') }}</p>
                </div>
                <div class="text-center p-4 bg-orange-50 rounded-lg border border-orange-200">
                    <p class="text-sm text-gray-600 mb-1">Last Activity</p>
                    <p class="text-lg font-bold text-orange-600">{{ $customer->updated_at->diffForHumans() }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Booking History -->
    <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
        <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
            <svg class="w-6 h-6 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
            </svg>
            Booking History
        </h2>
        
        @if($customer->bookings && $customer->bookings->count() > 0)
            <div class="space-y-4">
                @foreach($customer->bookings->take(5) as $booking)
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M21,6H3A1,1 0 0,0 2,7V17A1,1 0 0,0 3,18H21A1,1 0 0,0 22,17V7A1,1 0 0,0 21,6M20,16H4V8H20V16Z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">{{ $booking->booking_code }}</h3>
                                    <p class="text-sm text-gray-600">{{ $booking->console->name ?? 'Unknown Console' }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                @if($booking->status === 'active')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                @elseif($booking->status === 'completed')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Completed
                                    </span>
                                @elseif($booking->status === 'pending')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Pending
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        {{ ucfirst($booking->status) }}
                                    </span>
                                @endif
                                <p class="text-sm font-bold text-blue-600 mt-1">Rp {{ number_format($booking->total_cost) }}</p>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Date:</span>
                                <span class="font-medium ml-2">{{ $booking->start_time->format('d M Y') }}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Duration:</span>
                                <span class="font-medium ml-2">{{ $booking->duration_hours }} hours</span>
                            </div>
                        </div>
                        
                        <div class="mt-3 flex items-center justify-between">
                            <div class="text-xs text-gray-500">
                                {{ $booking->start_time->format('H:i') }} - {{ $booking->end_time->format('H:i') }}
                            </div>
                            <a href="{{ route('admin.bookings.show', $booking) }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                View Details →
                            </a>
                        </div>
                    </div>
                @endforeach
                
                @if($customer->bookings->count() > 5)
                    <div class="text-center">
                        <p class="text-gray-500">Showing 5 of {{ $customer->bookings->count() }} bookings</p>
                    </div>
                @endif
            </div>
        @else
            <div class="text-center py-8 bg-gray-50 rounded-lg">
                <svg class="w-12 h-12 text-gray-400 mx-auto mb-3" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
                </svg>
                <p class="text-gray-500">No booking history found</p>
            </div>
        @endif
    </div>

    <!-- Actions -->
    <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
        <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
            <svg class="w-6 h-6 mr-2 text-orange-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21V20A2,2 0 0,1 19,22H5A2,2 0 0,1 3,20V19H2A1,1 0 0,1 1,18V15A1,1 0 0,1 2,14H3A7,7 0 0,1 10,7H11V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2M7.5,13A2.5,2.5 0 0,0 5,15.5A2.5,2.5 0 0,0 7.5,18A2.5,2.5 0 0,0 10,15.5A2.5,2.5 0 0,0 7.5,13M16.5,13A2.5,2.5 0 0,0 14,15.5A2.5,2.5 0 0,0 16.5,18A2.5,2.5 0 0,0 19,15.5A2.5,2.5 0 0,0 16.5,13Z"/>
            </svg>
            Actions
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="{{ route('admin.customers.edit', $customer) }}" class="block w-full bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white px-6 py-3 rounded-lg font-semibold text-center transition-all duration-200 transform hover:scale-105">
                ✏️ Edit Customer
            </a>
            
            <!-- Membership Update -->
            <div class="relative">
                <button onclick="toggleMembershipForm()" class="w-full bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105">
                    👑 Update Membership
                </button>
            </div>
            
            <!-- Back Button -->
            <a href="{{ route('admin.customers.index') }}" class="block w-full bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-6 py-3 rounded-lg font-semibold text-center transition-all duration-200">
                ← Back to Customers
            </a>
        </div>
        
        <!-- Membership Update Form (Hidden by default) -->
        <div id="membershipForm" class="hidden mt-6 bg-purple-50 rounded-lg p-4 border border-purple-200">
            <h3 class="font-semibold text-gray-900 mb-4">Update Membership</h3>
            <form action="{{ route('admin.customers.membership', $customer) }}" method="POST" class="space-y-4">
                @csrf
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="membership_type" class="block text-sm font-medium text-gray-700 mb-1">Membership Type</label>
                        <select name="membership_type" id="membership_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
                            <option value="regular" {{ $customer->membership_type === 'regular' ? 'selected' : '' }}>Regular</option>
                            <option value="silver" {{ $customer->membership_type === 'silver' ? 'selected' : '' }}>Silver</option>
                            <option value="gold" {{ $customer->membership_type === 'gold' ? 'selected' : '' }}>Gold</option>
                            <option value="platinum" {{ $customer->membership_type === 'platinum' ? 'selected' : '' }}>Platinum</option>
                        </select>
                    </div>
                    <div>
                        <label for="membership_expires" class="block text-sm font-medium text-gray-700 mb-1">Expires Date</label>
                        <input type="date" name="membership_expires" id="membership_expires" 
                               value="{{ $customer->membership_expires ? $customer->membership_expires->format('Y-m-d') : '' }}"
                               min="{{ now()->format('Y-m-d') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                        Update Membership
                    </button>
                    <button type="button" onclick="toggleMembershipForm()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleMembershipForm() {
    const form = document.getElementById('membershipForm');
    form.classList.toggle('hidden');
}
</script>
@endsection
