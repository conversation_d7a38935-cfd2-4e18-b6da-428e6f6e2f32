<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Console;
use App\Models\Game;
use App\Models\Customer;
use App\Models\Booking;
use Carbon\Carbon;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample users
        $adminUser = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'Admin PlayStation',
            'password' => bcrypt('password'),
            'role' => 'admin'
        ]);

        $customerUser1 = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => '<PERSON> Doe',
            'password' => bcrypt('password'),
            'role' => 'user'
        ]);

        $customerUser2 = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => '<PERSON>',
            'password' => bcrypt('password'),
            'role' => 'user'
        ]);

        $customerUser3 = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => '<PERSON>',
            'password' => bcrypt('password'),
            'role' => 'user'
        ]);

        // Create sample consoles
        $consoles = [
            [
                'name' => 'PlayStation 5 #001',
                'type' => 'PlayStation 5',
                'serial_number' => 'PS5-001-2024',
                'status' => 'available',
                'hourly_rate' => 25000,
                'daily_rate' => 180000,
                'controller_count' => 2,
                'specifications' => [
                    'cpu' => 'AMD Zen 2 8-core',
                    'gpu' => 'AMD RDNA 2',
                    'storage' => '825GB SSD',
                    'ram' => '16GB GDDR6',
                    'resolution' => '4K UHD'
                ],
                'description' => 'Latest PlayStation 5 console with ultra-fast SSD and ray tracing support.'
            ],
            [
                'name' => 'PlayStation 5 #002',
                'type' => 'PlayStation 5',
                'serial_number' => 'PS5-002-2024',
                'status' => 'rented',
                'hourly_rate' => 25000,
                'daily_rate' => 180000,
                'controller_count' => 2,
                'specifications' => [
                    'cpu' => 'AMD Zen 2 8-core',
                    'gpu' => 'AMD RDNA 2',
                    'storage' => '825GB SSD',
                    'ram' => '16GB GDDR6',
                    'resolution' => '4K UHD'
                ],
                'description' => 'Latest PlayStation 5 console with ultra-fast SSD and ray tracing support.'
            ],
            [
                'name' => 'PlayStation 4 Pro #001',
                'type' => 'PlayStation 4 Pro',
                'serial_number' => 'PS4P-001-2024',
                'status' => 'available',
                'hourly_rate' => 20000,
                'daily_rate' => 140000,
                'controller_count' => 2,
                'specifications' => [
                    'cpu' => 'AMD Jaguar 8-core',
                    'gpu' => 'AMD Radeon',
                    'storage' => '1TB HDD',
                    'ram' => '8GB GDDR5',
                    'resolution' => '4K HDR'
                ],
                'description' => 'Enhanced PlayStation 4 with 4K gaming capabilities.'
            ],
            [
                'name' => 'PlayStation 4 Slim #001',
                'type' => 'PlayStation 4 Slim',
                'serial_number' => 'PS4S-001-2024',
                'status' => 'available',
                'hourly_rate' => 15000,
                'daily_rate' => 100000,
                'controller_count' => 2,
                'specifications' => [
                    'cpu' => 'AMD Jaguar 8-core',
                    'gpu' => 'AMD Radeon',
                    'storage' => '500GB HDD',
                    'ram' => '8GB GDDR5',
                    'resolution' => '1080p Full HD'
                ],
                'description' => 'Compact and efficient PlayStation 4 for great gaming experience.'
            ],
            [
                'name' => 'PlayStation 4 Slim #002',
                'type' => 'PlayStation 4 Slim',
                'serial_number' => 'PS4S-002-2024',
                'status' => 'maintenance',
                'hourly_rate' => 15000,
                'daily_rate' => 100000,
                'controller_count' => 1,
                'specifications' => [
                    'cpu' => 'AMD Jaguar 8-core',
                    'gpu' => 'AMD Radeon',
                    'storage' => '500GB HDD',
                    'ram' => '8GB GDDR5',
                    'resolution' => '1080p Full HD'
                ],
                'description' => 'Compact and efficient PlayStation 4 for great gaming experience.'
            ]
        ];

        foreach ($consoles as $consoleData) {
            Console::firstOrCreate([
                'serial_number' => $consoleData['serial_number']
            ], $consoleData);
        }

        // Create sample games
        $games = [
            [
                'title' => 'Spider-Man: Miles Morales',
                'genre' => 'Action',
                'platform' => 'PS5',
                'release_year' => 2020,
                'rating' => 'T',
                'rating_score' => 8.5,
                'rental_price' => 5000,
                'stock' => 3,
                'available_stock' => 2,
                'is_popular' => true,
                'description' => 'Experience the rise of Miles Morales as the new hero masters incredible, explosive new powers.'
            ],
            [
                'title' => 'God of War',
                'genre' => 'Action',
                'platform' => 'Both',
                'release_year' => 2018,
                'rating' => 'M',
                'rating_score' => 9.5,
                'rental_price' => 4000,
                'stock' => 4,
                'available_stock' => 3,
                'is_popular' => true,
                'description' => 'Kratos and his son Atreus embark on a journey through Norse mythology.'
            ],
            [
                'title' => 'The Last of Us Part II',
                'genre' => 'Action',
                'platform' => 'PS4',
                'release_year' => 2020,
                'rating' => 'M',
                'rating_score' => 9.0,
                'rental_price' => 4500,
                'stock' => 3,
                'available_stock' => 2,
                'is_popular' => true,
                'description' => 'Five years after their dangerous journey across the post-pandemic United States.'
            ],
            [
                'title' => 'FIFA 24',
                'genre' => 'Sports',
                'platform' => 'Both',
                'release_year' => 2023,
                'rating' => 'E',
                'rating_score' => 8.0,
                'rental_price' => 3500,
                'stock' => 5,
                'available_stock' => 4,
                'is_popular' => true,
                'description' => 'The latest FIFA game with updated teams and improved gameplay.'
            ],
            [
                'title' => 'Call of Duty: Modern Warfare III',
                'genre' => 'FPS',
                'platform' => 'Both',
                'release_year' => 2023,
                'rating' => 'M',
                'rating_score' => 7.5,
                'rental_price' => 4000,
                'stock' => 3,
                'available_stock' => 2,
                'is_popular' => false,
                'description' => 'The latest installment in the Modern Warfare series.'
            ]
        ];

        foreach ($games as $gameData) {
            Game::firstOrCreate([
                'title' => $gameData['title']
            ], $gameData);
        }

        // Create sample customers
        $customers = [
            [
                'user_id' => $customerUser1->id,
                'phone' => '+62812-3456-7890',
                'address' => 'Jl. Sudirman No. 123, Jakarta Pusat',
                'birth_date' => '1995-05-15',
                'membership_type' => 'gold',
                'membership_expires' => now()->addYear(),
                'total_spent' => 850000,
                'is_active' => true
            ],
            [
                'user_id' => $customerUser2->id,
                'phone' => '+62813-9876-5432',
                'address' => 'Jl. Thamrin No. 456, Jakarta Pusat',
                'birth_date' => '1992-08-22',
                'membership_type' => 'platinum',
                'membership_expires' => now()->addYear(),
                'total_spent' => 1250000,
                'is_active' => true
            ],
            [
                'user_id' => $customerUser3->id,
                'phone' => '+62814-1111-2222',
                'address' => 'Jl. Gatot Subroto No. 789, Jakarta Selatan',
                'birth_date' => '1988-12-10',
                'membership_type' => 'silver',
                'membership_expires' => now()->addMonths(6),
                'total_spent' => 450000,
                'is_active' => true
            ]
        ];

        foreach ($customers as $customerData) {
            Customer::firstOrCreate([
                'user_id' => $customerData['user_id']
            ], $customerData);
        }

        // Create sample bookings
        $ps5Console = Console::where('type', 'PlayStation 5')->first();
        $ps4ProConsole = Console::where('type', 'PlayStation 4 Pro')->first();
        $ps4SlimConsole = Console::where('type', 'PlayStation 4 Slim')->first();

        $spiderManGame = Game::where('title', 'Spider-Man: Miles Morales')->first();
        $godOfWarGame = Game::where('title', 'God of War')->first();
        $fifaGame = Game::where('title', 'FIFA 24')->first();

        $bookings = [
            [
                'booking_code' => 'PS' . date('Ymd') . 'A001',
                'user_id' => $customerUser1->id,
                'console_id' => $ps5Console->id,
                'start_time' => now()->addHours(2),
                'end_time' => now()->addHours(6),
                'duration_hours' => 4,
                'console_cost' => 100000,
                'games_cost' => 20000,
                'total_cost' => 108000, // With gold discount (10%)
                'deposit' => 21600,
                'status' => 'confirmed',
                'payment_status' => 'paid',
                'notes' => 'Customer requested extra controller'
            ],
            [
                'booking_code' => 'PS' . date('Ymd') . 'A002',
                'user_id' => $customerUser2->id,
                'console_id' => $ps4ProConsole->id,
                'start_time' => now()->subHours(2),
                'end_time' => now()->addHours(4),
                'duration_hours' => 6,
                'console_cost' => 120000,
                'games_cost' => 24000,
                'total_cost' => 122400, // With platinum discount (15%)
                'deposit' => 24480,
                'status' => 'active',
                'payment_status' => 'paid',
                'notes' => 'VIP customer - priority service'
            ],
            [
                'booking_code' => 'PS' . date('Ymd') . 'A003',
                'user_id' => $customerUser3->id,
                'console_id' => $ps4SlimConsole->id,
                'start_time' => now()->addDays(1),
                'end_time' => now()->addDays(1)->addHours(3),
                'duration_hours' => 3,
                'console_cost' => 45000,
                'games_cost' => 10500,
                'total_cost' => 52725, // With silver discount (5%)
                'deposit' => 10545,
                'status' => 'pending',
                'payment_status' => 'unpaid',
                'notes' => 'First time customer'
            ],
            [
                'booking_code' => 'PS' . date('Ymd') . 'A004',
                'user_id' => $customerUser1->id,
                'console_id' => $ps5Console->id,
                'start_time' => now()->subDays(2),
                'end_time' => now()->subDays(2)->addHours(5),
                'duration_hours' => 5,
                'console_cost' => 125000,
                'games_cost' => 25000,
                'total_cost' => 135000, // With gold discount (10%)
                'deposit' => 27000,
                'status' => 'completed',
                'payment_status' => 'paid',
                'notes' => 'Completed successfully'
            ]
        ];

        foreach ($bookings as $index => $bookingData) {
            // Add game_ids to booking data
            $gameIds = [];
            if ($index === 0 && $spiderManGame && $godOfWarGame) { // PS5 booking
                $gameIds = [$spiderManGame->id, $godOfWarGame->id];
            } elseif ($index === 1 && $godOfWarGame && $fifaGame) { // PS4 Pro booking
                $gameIds = [$godOfWarGame->id, $fifaGame->id];
            } elseif ($index === 2 && $fifaGame) { // PS4 Slim booking
                $gameIds = [$fifaGame->id];
            } elseif ($index === 3 && $spiderManGame && $godOfWarGame) { // Completed PS5 booking
                $gameIds = [$spiderManGame->id, $godOfWarGame->id];
            }

            $bookingData['game_ids'] = $gameIds;

            Booking::firstOrCreate([
                'booking_code' => $bookingData['booking_code']
            ], $bookingData);
        }

        $this->command->info('Sample data created successfully!');
        $this->command->info('Admin login: <EMAIL> / password');
        $this->command->info('Customer logins: <EMAIL>, <EMAIL>, <EMAIL> / password');
    }
}
