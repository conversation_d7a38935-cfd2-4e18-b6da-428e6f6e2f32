<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Gaming Header -->
    <div class="bg-gradient-to-r from-pink-900 via-red-900 to-orange-900 rounded-2xl shadow-xl p-8 text-white relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full floating"></div>
            <div class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full floating" style="animation-delay: 1s;"></div>
        </div>
        
        <div class="relative z-10 flex items-center justify-between">
            <div>
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-4 pulse-glow">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M7.5,4A5.5,5.5 0 0,0 2,9.5C2,10 2.04,10.5 2.12,11H6.5L7.66,8.5H8.34L9.5,11H13.88C13.96,10.5 14,10 14,9.5A5.5,5.5 0 0,0 8.5,4H7.5M21.8,12H2.2L1.27,21H22.73L21.8,12Z"/>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold gaming-font neon-text">Edit Game</h1>
                        <p class="text-pink-100 text-lg"><?php echo e($game->title); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Game Form -->
    <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
        <form action="<?php echo e(route('admin.games.update', $game)); ?>" method="POST" class="space-y-6">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            
            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Game Title *</label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           value="<?php echo e(old('title', $game->title)); ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           placeholder="e.g., Spider-Man: Miles Morales"
                           required>
                    <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div>
                    <label for="genre" class="block text-sm font-medium text-gray-700 mb-2">Genre *</label>
                    <select id="genre" 
                            name="genre" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 <?php $__errorArgs = ['genre'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            required>
                        <option value="">Select Genre</option>
                        <option value="Action" <?php echo e(old('genre', $game->genre) === 'Action' ? 'selected' : ''); ?>>Action</option>
                        <option value="Adventure" <?php echo e(old('genre', $game->genre) === 'Adventure' ? 'selected' : ''); ?>>Adventure</option>
                        <option value="RPG" <?php echo e(old('genre', $game->genre) === 'RPG' ? 'selected' : ''); ?>>RPG</option>
                        <option value="Sports" <?php echo e(old('genre', $game->genre) === 'Sports' ? 'selected' : ''); ?>>Sports</option>
                        <option value="Racing" <?php echo e(old('genre', $game->genre) === 'Racing' ? 'selected' : ''); ?>>Racing</option>
                        <option value="FPS" <?php echo e(old('genre', $game->genre) === 'FPS' ? 'selected' : ''); ?>>FPS</option>
                        <option value="Strategy" <?php echo e(old('genre', $game->genre) === 'Strategy' ? 'selected' : ''); ?>>Strategy</option>
                        <option value="Simulation" <?php echo e(old('genre', $game->genre) === 'Simulation' ? 'selected' : ''); ?>>Simulation</option>
                        <option value="Horror" <?php echo e(old('genre', $game->genre) === 'Horror' ? 'selected' : ''); ?>>Horror</option>
                        <option value="Fighting" <?php echo e(old('genre', $game->genre) === 'Fighting' ? 'selected' : ''); ?>>Fighting</option>
                    </select>
                    <?php $__errorArgs = ['genre'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div>
                    <label for="platform" class="block text-sm font-medium text-gray-700 mb-2">Platform *</label>
                    <select id="platform" 
                            name="platform" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 <?php $__errorArgs = ['platform'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            required>
                        <option value="">Select Platform</option>
                        <option value="PS5" <?php echo e(old('platform', $game->platform) === 'PS5' ? 'selected' : ''); ?>>PlayStation 5 Only</option>
                        <option value="PS4" <?php echo e(old('platform', $game->platform) === 'PS4' ? 'selected' : ''); ?>>PlayStation 4 Only</option>
                        <option value="Both" <?php echo e(old('platform', $game->platform) === 'Both' ? 'selected' : ''); ?>>Both PS4 & PS5</option>
                    </select>
                    <?php $__errorArgs = ['platform'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div>
                    <label for="release_year" class="block text-sm font-medium text-gray-700 mb-2">Release Year *</label>
                    <input type="number" 
                           id="release_year" 
                           name="release_year" 
                           value="<?php echo e(old('release_year', $game->release_year)); ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 <?php $__errorArgs = ['release_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           min="1990"
                           max="<?php echo e(date('Y') + 2); ?>"
                           required>
                    <?php $__errorArgs = ['release_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            <!-- Rating & Pricing -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div>
                    <label for="rating" class="block text-sm font-medium text-gray-700 mb-2">Age Rating *</label>
                    <select id="rating" 
                            name="rating" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 <?php $__errorArgs = ['rating'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            required>
                        <option value="">Select Rating</option>
                        <option value="E" <?php echo e(old('rating', $game->rating) === 'E' ? 'selected' : ''); ?>>E - Everyone</option>
                        <option value="E10+" <?php echo e(old('rating', $game->rating) === 'E10+' ? 'selected' : ''); ?>>E10+ - Everyone 10+</option>
                        <option value="T" <?php echo e(old('rating', $game->rating) === 'T' ? 'selected' : ''); ?>>T - Teen</option>
                        <option value="M" <?php echo e(old('rating', $game->rating) === 'M' ? 'selected' : ''); ?>>M - Mature 17+</option>
                        <option value="AO" <?php echo e(old('rating', $game->rating) === 'AO' ? 'selected' : ''); ?>>AO - Adults Only</option>
                    </select>
                    <?php $__errorArgs = ['rating'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div>
                    <label for="rating_score" class="block text-sm font-medium text-gray-700 mb-2">Rating Score</label>
                    <input type="number" 
                           id="rating_score" 
                           name="rating_score" 
                           value="<?php echo e(old('rating_score', $game->rating_score)); ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 <?php $__errorArgs = ['rating_score'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           min="0"
                           max="10"
                           step="0.1"
                           placeholder="8.5">
                    <?php $__errorArgs = ['rating_score'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div>
                    <label for="rental_price" class="block text-sm font-medium text-gray-700 mb-2">Rental Price (Rp/hour) *</label>
                    <input type="number" 
                           id="rental_price" 
                           name="rental_price" 
                           value="<?php echo e(old('rental_price', $game->rental_price)); ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 <?php $__errorArgs = ['rental_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           min="0"
                           step="500"
                           placeholder="5000"
                           required>
                    <?php $__errorArgs = ['rental_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" 
                           id="is_popular" 
                           name="is_popular" 
                           value="1"
                           <?php echo e(old('is_popular', $game->is_popular) ? 'checked' : ''); ?>

                           class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                    <label for="is_popular" class="ml-3 text-sm font-medium text-gray-700">
                        ⭐ Mark as Popular
                    </label>
                </div>
            </div>

            <!-- Stock Management -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="stock" class="block text-sm font-medium text-gray-700 mb-2">Total Stock *</label>
                    <input type="number" 
                           id="stock" 
                           name="stock" 
                           value="<?php echo e(old('stock', $game->stock)); ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 <?php $__errorArgs = ['stock'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           min="1"
                           max="50"
                           required>
                    <?php $__errorArgs = ['stock'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div>
                    <label for="available_stock" class="block text-sm font-medium text-gray-700 mb-2">Available Stock *</label>
                    <input type="number" 
                           id="available_stock" 
                           name="available_stock" 
                           value="<?php echo e(old('available_stock', $game->available_stock)); ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 <?php $__errorArgs = ['available_stock'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           min="0"
                           max="50"
                           required>
                    <?php $__errorArgs = ['available_stock'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea id="description" 
                          name="description" 
                          rows="4"
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                          placeholder="Describe the game story, gameplay, and features..."><?php echo e(old('description', $game->description)); ?></textarea>
                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <div class="flex items-center space-x-4">
                    <a href="<?php echo e(route('admin.games.index')); ?>" 
                       class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                        ← Back to List
                    </a>
                    
                    <a href="<?php echo e(route('admin.games.show', $game)); ?>" 
                       class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                        👁️ View Details
                    </a>
                </div>
                
                <div class="flex items-center space-x-4">
                    <form action="<?php echo e(route('admin.games.destroy', $game)); ?>" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this game?')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" 
                                class="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                            🗑️ Delete
                        </button>
                    </form>
                    
                    <button type="submit" 
                            class="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg">
                        <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z"/>
                        </svg>
                        Update Game
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Auto-sync available stock with total stock
document.getElementById('stock').addEventListener('input', function() {
    const totalStock = parseInt(this.value) || 0;
    const availableStockInput = document.getElementById('available_stock');
    const currentAvailable = parseInt(availableStockInput.value) || 0;
    
    // If available stock is greater than total stock, adjust it
    if (currentAvailable > totalStock) {
        availableStockInput.value = totalStock;
    }
    
    // Set max attribute for available stock
    availableStockInput.setAttribute('max', totalStock);
});

// Validate available stock doesn't exceed total stock
document.getElementById('available_stock').addEventListener('input', function() {
    const totalStock = parseInt(document.getElementById('stock').value) || 0;
    const availableStock = parseInt(this.value) || 0;
    
    if (availableStock > totalStock) {
        this.value = totalStock;
        alert('Available stock cannot exceed total stock!');
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\TokoRentalPlayStation\resources\views/games/edit.blade.php ENDPATH**/ ?>