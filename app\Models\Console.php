<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Console extends Model
{
    protected $fillable = [
        'name',
        'type',
        'description',
        'image',
        'hourly_rate',
        'daily_rate',
        'status',
        'serial_number',
        'specifications',
        'controller_count',
    ];

    protected $casts = [
        'specifications' => 'array',
        'hourly_rate' => 'decimal:2',
        'daily_rate' => 'decimal:2',
    ];

    /**
     * Get the bookings for the console.
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Check if console is available
     */
    public function isAvailable(): bool
    {
        return $this->status === 'available';
    }

    /**
     * Get current active booking
     */
    public function currentBooking()
    {
        return $this->bookings()
            ->where('status', 'active')
            ->where('start_time', '<=', now())
            ->where('end_time', '>=', now())
            ->first();
    }

    /**
     * Get upcoming bookings
     */
    public function upcomingBookings()
    {
        return $this->bookings()
            ->where('status', 'confirmed')
            ->where('start_time', '>', now())
            ->orderBy('start_time')
            ->get();
    }
}
