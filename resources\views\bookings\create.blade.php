@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="bg-gradient-to-r from-purple-900 via-blue-900 to-indigo-900 rounded-2xl shadow-xl p-8 text-white relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-4 right-4 w-20 h-20 bg-white rounded-full animate-pulse"></div>
            <div class="absolute bottom-4 left-4 w-16 h-16 bg-white rounded-full animate-pulse delay-1000"></div>
        </div>
        
        <div class="relative z-10">
            <div class="flex items-center mb-4">
                <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-4">
                    <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                    </svg>
                </div>
                <div>
                    <h1 class="text-4xl font-bold">Book Your Gaming Session</h1>
                    <p class="text-purple-100 text-lg">Reserve your PlayStation console and games</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Booking Form -->
    <div class="bg-white rounded-xl shadow-sm p-8">
        @if(auth()->user()->isAdmin())
            <form action="{{ route('admin.bookings.store') }}" method="POST" class="space-y-8">
        @else
            <form action="{{ route('user.bookings.store') }}" method="POST" class="space-y-8">
        @endif
            @csrf

            <!-- Console Selection -->
            <div>
                <label class="block text-lg font-semibold text-gray-900 mb-4">
                    <svg class="w-6 h-6 inline mr-2 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M21,6H3A1,1 0 0,0 2,7V17A1,1 0 0,0 3,18H21A1,1 0 0,0 22,17V7A1,1 0 0,0 21,6M20,16H4V8H20V16Z"/>
                    </svg>
                    Choose Your Console
                </label>
                
                @if($availableConsoles->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($availableConsoles as $console)
                            <label class="relative cursor-pointer">
                                <input type="radio" name="console_id" value="{{ $console->id }}" class="sr-only peer" required>
                                <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border-2 border-gray-200 peer-checked:border-blue-500 peer-checked:bg-gradient-to-br peer-checked:from-blue-100 peer-checked:to-indigo-100 transition-all duration-200 hover:shadow-lg">
                                    <div class="flex items-center mb-4">
                                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M21,6H3A1,1 0 0,0 2,7V17A1,1 0 0,0 3,18H21A1,1 0 0,0 22,17V7A1,1 0 0,0 21,6M20,16H4V8H20V16Z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-gray-900">{{ $console->name }}</h3>
                                            <p class="text-sm text-gray-600">{{ $console->type }}</p>
                                        </div>
                                    </div>
                                    
                                    <div class="space-y-2 text-sm">
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Hourly Rate:</span>
                                            <span class="font-medium text-blue-600">Rp {{ number_format($console->hourly_rate) }}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Daily Rate:</span>
                                            <span class="font-medium text-blue-600">Rp {{ number_format($console->daily_rate) }}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Controllers:</span>
                                            <span class="font-medium">{{ $console->controller_count }}</span>
                                        </div>
                                    </div>
                                    
                                    <!-- Selected indicator -->
                                    <div class="absolute top-3 right-3 w-6 h-6 bg-blue-600 rounded-full hidden peer-checked:flex items-center justify-center">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                </div>
                            </label>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8 bg-gray-50 rounded-lg">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M21,6H3A1,1 0 0,0 2,7V17A1,1 0 0,0 3,18H21A1,1 0 0,0 22,17V7A1,1 0 0,0 21,6M20,16H4V8H20V16Z"/>
                        </svg>
                        <p class="text-gray-500">No consoles available at the moment</p>
                    </div>
                @endif
                @error('console_id')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Date & Time Selection -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="start_time" class="block text-sm font-medium text-gray-700 mb-2">
                        <svg class="w-4 h-4 inline mr-1 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
                        </svg>
                        Start Date & Time
                    </label>
                    <input type="datetime-local" 
                           id="start_time" 
                           name="start_time" 
                           value="{{ old('start_time') }}"
                           min="{{ now()->addHour()->format('Y-m-d\TH:i') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('start_time') border-red-500 @enderror"
                           required>
                    @error('start_time')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="duration_hours" class="block text-sm font-medium text-gray-700 mb-2">
                        <svg class="w-4 h-4 inline mr-1 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6Z"/>
                        </svg>
                        Duration (Hours)
                    </label>
                    <select id="duration_hours" 
                            name="duration_hours" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('duration_hours') border-red-500 @enderror"
                            required>
                        <option value="">Select duration</option>
                        @for($i = 1; $i <= 12; $i++)
                            <option value="{{ $i }}" {{ old('duration_hours') == $i ? 'selected' : '' }}>
                                {{ $i }} {{ $i == 1 ? 'hour' : 'hours' }}
                                @if($i >= 8)
                                    ({{ number_format($i / 8, 1) }} day{{ $i > 8 ? 's' : '' }})
                                @endif
                            </option>
                        @endfor
                    </select>
                    @error('duration_hours')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Game Selection -->
            <div>
                <label class="block text-lg font-semibold text-gray-900 mb-4">
                    <svg class="w-6 h-6 inline mr-2 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M7.5,4A5.5,5.5 0 0,0 2,9.5C2,10 2.04,10.5 2.12,11H6.5L7.66,8.5H8.34L9.5,11H13.88C13.96,10.5 14,10 14,9.5A5.5,5.5 0 0,0 8.5,4H7.5M21.8,12H2.2L1.27,21H22.73L21.8,12Z"/>
                    </svg>
                    Select Games (Optional)
                </label>
                
                @if($games->count() > 0)
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                        @foreach($games as $game)
                            <label class="relative cursor-pointer">
                                <input type="checkbox" name="game_ids[]" value="{{ $game->id }}" class="sr-only peer">
                                <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg p-4 border-2 border-gray-200 peer-checked:border-purple-500 peer-checked:bg-gradient-to-br peer-checked:from-purple-100 peer-checked:to-pink-100 transition-all duration-200 hover:shadow-md">
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M7.5,4A5.5,5.5 0 0,0 2,9.5C2,10 2.04,10.5 2.12,11H6.5L7.66,8.5H8.34L9.5,11H13.88C13.96,10.5 14,10 14,9.5A5.5,5.5 0 0,0 8.5,4H7.5M21.8,12H2.2L1.27,21H22.73L21.8,12Z"/>
                                            </svg>
                                        </div>
                                        <h3 class="font-medium text-gray-900 text-sm mb-1">{{ $game->title }}</h3>
                                        <p class="text-xs text-gray-600 mb-2">{{ $game->genre }}</p>
                                        <p class="text-xs font-medium text-purple-600">+Rp {{ number_format($game->rental_price) }}/hr</p>
                                        
                                        <!-- Selected indicator -->
                                        <div class="absolute top-2 right-2 w-5 h-5 bg-purple-600 rounded-full hidden peer-checked:flex items-center justify-center">
                                            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </label>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-6 bg-gray-50 rounded-lg">
                        <p class="text-gray-500">No games available</p>
                    </div>
                @endif
            </div>

            <!-- Cost Estimation -->
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Cost Estimation</h3>
                <div id="cost-breakdown" class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Console Cost:</span>
                        <span id="console-cost" class="font-medium">Rp 0</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Games Cost:</span>
                        <span id="games-cost" class="font-medium">Rp 0</span>
                    </div>
                    <div class="border-t border-blue-200 pt-2 mt-2">
                        <div class="flex justify-between text-lg font-semibold">
                            <span class="text-gray-900">Total Cost:</span>
                            <span id="total-cost" class="text-blue-600">Rp 0</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <div class="flex items-center space-x-4">
                    @if(auth()->user()->isAdmin())
                        <a href="{{ route('admin.bookings.index') }}" class="text-gray-600 hover:text-gray-700 font-medium">Cancel</a>
                    @else
                        <a href="{{ route('user.bookings.index') }}" class="text-gray-600 hover:text-gray-700 font-medium">Cancel</a>
                    @endif
                </div>
                <button type="submit" 
                        class="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg">
                    <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                    </svg>
                    Create Booking
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Cost calculation
document.addEventListener('DOMContentLoaded', function() {
    const consoleRadios = document.querySelectorAll('input[name="console_id"]');
    const durationSelect = document.getElementById('duration_hours');
    const gameCheckboxes = document.querySelectorAll('input[name="game_ids[]"]');
    
    function updateCost() {
        const selectedConsole = document.querySelector('input[name="console_id"]:checked');
        const duration = parseInt(durationSelect.value) || 0;
        
        let consoleCost = 0;
        let gamesCost = 0;
        
        if (selectedConsole && duration > 0) {
            const consoleData = @json($availableConsoles->keyBy('id'));
            const console = consoleData[selectedConsole.value];
            consoleCost = console.hourly_rate * duration;
        }
        
        const selectedGames = Array.from(gameCheckboxes).filter(cb => cb.checked);
        if (selectedGames.length > 0 && duration > 0) {
            const gamesData = @json($games->keyBy('id'));
            gamesCost = selectedGames.reduce((total, cb) => {
                return total + (gamesData[cb.value].rental_price * duration);
            }, 0);
        }
        
        const totalCost = consoleCost + gamesCost;
        
        document.getElementById('console-cost').textContent = 'Rp ' + consoleCost.toLocaleString();
        document.getElementById('games-cost').textContent = 'Rp ' + gamesCost.toLocaleString();
        document.getElementById('total-cost').textContent = 'Rp ' + totalCost.toLocaleString();
    }
    
    consoleRadios.forEach(radio => radio.addEventListener('change', updateCost));
    durationSelect.addEventListener('change', updateCost);
    gameCheckboxes.forEach(checkbox => checkbox.addEventListener('change', updateCost));
});
</script>
@endsection
