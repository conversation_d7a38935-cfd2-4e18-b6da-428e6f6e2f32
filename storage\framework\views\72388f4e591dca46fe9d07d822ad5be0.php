<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="bg-gradient-to-r from-blue-900 via-blue-800 to-indigo-900 rounded-2xl shadow-xl p-8 text-white relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full"></div>
            <div class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full"></div>
        </div>
        
        <div class="relative z-10 flex items-center justify-between">
            <div>
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-4">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold">
                            <?php if(auth()->user()->isAdmin()): ?>
                                All Bookings
                            <?php else: ?>
                                My Bookings
                            <?php endif; ?>
                        </h1>
                        <p class="text-blue-100 text-lg">Manage PlayStation rental bookings</p>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <?php if(auth()->user()->isAdmin()): ?>
                    <a href="<?php echo e(route('admin.bookings.create')); ?>" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 border border-white/30">
                <?php else: ?>
                    <a href="<?php echo e(route('user.bookings.create')); ?>" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 border border-white/30">
                <?php endif; ?>
                    <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                    </svg>
                    New Booking
                </a>
            </div>
        </div>
    </div>

    <!-- Bookings List -->
    <div class="bg-white rounded-xl shadow-sm">
        <?php if($bookings->count() > 0): ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Booking Code</th>
                            <?php if(auth()->user()->isAdmin()): ?>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                            <?php endif; ?>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Console</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Cost</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php $__currentLoopData = $bookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($booking->booking_code); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($booking->created_at->format('d M Y')); ?></div>
                                </td>
                                <?php if(auth()->user()->isAdmin()): ?>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo e($booking->user->name ?? 'Unknown'); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo e($booking->user->email ?? 'N/A'); ?></div>
                                    </td>
                                <?php endif; ?>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                            <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M21,6H3A1,1 0 0,0 2,7V17A1,1 0 0,0 3,18H21A1,1 0 0,0 22,17V7A1,1 0 0,0 21,6M20,16H4V8H20V16Z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900"><?php echo e($booking->console->name ?? 'Unknown Console'); ?></div>
                                            <div class="text-sm text-gray-500"><?php echo e($booking->console->type ?? 'N/A'); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo e($booking->start_time->format('d M Y')); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($booking->start_time->format('H:i')); ?> - <?php echo e($booking->end_time->format('H:i')); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo e($booking->duration_hours); ?> hours</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">Rp <?php echo e(number_format($booking->total_cost)); ?></div>
                                    <div class="text-sm text-gray-500">
                                        <?php if($booking->payment_status === 'paid'): ?>
                                            <span class="text-green-600">Paid</span>
                                        <?php elseif($booking->payment_status === 'partial'): ?>
                                            <span class="text-yellow-600">Partial</span>
                                        <?php else: ?>
                                            <span class="text-red-600">Unpaid</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if($booking->status === 'active'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    <?php elseif($booking->status === 'pending'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Pending
                                        </span>
                                    <?php elseif($booking->status === 'confirmed'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Confirmed
                                        </span>
                                    <?php elseif($booking->status === 'completed'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            Completed
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <?php echo e(ucfirst($booking->status)); ?>

                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex items-center justify-end space-x-2">
                                        <?php if(auth()->user()->isAdmin()): ?>
                                            <a href="<?php echo e(route('admin.bookings.show', $booking)); ?>" class="text-blue-600 hover:text-blue-900">View</a>
                                            <?php if($booking->status === 'pending'): ?>
                                                <a href="<?php echo e(route('admin.bookings.edit', $booking)); ?>" class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <a href="<?php echo e(route('user.bookings.show', $booking)); ?>" class="text-blue-600 hover:text-blue-900">View</a>
                                            <?php if($booking->status === 'pending'): ?>
                                                <a href="<?php echo e(route('user.bookings.edit', $booking)); ?>" class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if($bookings->hasPages()): ?>
                <div class="px-6 py-4 border-t border-gray-200">
                    <?php echo e($bookings->links()); ?>

                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="text-center py-12">
                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No bookings found</h3>
                <p class="text-gray-500 mb-4">
                    <?php if(auth()->user()->isAdmin()): ?>
                        No bookings have been created yet.
                    <?php else: ?>
                        You haven't made any bookings yet.
                    <?php endif; ?>
                </p>
                <?php if(auth()->user()->isAdmin()): ?>
                    <a href="<?php echo e(route('admin.bookings.create')); ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                <?php else: ?>
                    <a href="<?php echo e(route('user.bookings.create')); ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                <?php endif; ?>
                    Create First Booking
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\TokoRentalPlayStation\resources\views/bookings/index.blade.php ENDPATH**/ ?>