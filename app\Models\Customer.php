<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    protected $fillable = [
        'user_id',
        'phone',
        'address',
        'birth_date',
        'membership_type',
        'membership_expires',
        'total_spent',
        'total_bookings',
        'preferences',
        'is_active',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'membership_expires' => 'date',
        'total_spent' => 'decimal:2',
        'preferences' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user that owns the customer.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the bookings for the customer.
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class, 'user_id', 'user_id');
    }

    /**
     * Check if membership is active
     */
    public function hasActiveMembership(): bool
    {
        return $this->membership_expires && $this->membership_expires->isFuture();
    }

    /**
     * Get membership discount percentage
     */
    public function getMembershipDiscount(): float
    {
        if (!$this->hasActiveMembership()) {
            return 0;
        }

        return match($this->membership_type) {
            'silver' => 5,
            'gold' => 10,
            'platinum' => 15,
            default => 0,
        };
    }

    /**
     * Update total spent and bookings
     */
    public function updateStats(float $amount): void
    {
        $this->increment('total_spent', $amount);
        $this->increment('total_bookings');
    }
}
