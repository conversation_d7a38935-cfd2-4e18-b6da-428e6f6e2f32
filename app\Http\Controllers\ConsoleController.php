<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Console;

class ConsoleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $consoles = Console::withCount(['bookings as total_bookings'])
            ->latest()
            ->paginate(12);

        return view('consoles.index', compact('consoles'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        return view('consoles.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|string|max:255',
            'hourly_rate' => 'required|numeric|min:0',
            'daily_rate' => 'required|numeric|min:0',
            'serial_number' => 'required|string|unique:consoles,serial_number',
            'controller_count' => 'required|integer|min:1|max:4',
            'specifications' => 'nullable|array',
        ]);

        Console::create($request->all());

        return redirect()->route('admin.consoles.index')
            ->with('success', 'Console created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Console $console)
    {
        $console->load(['bookings.user']);
        $currentBooking = $console->currentBooking();
        $upcomingBookings = $console->upcomingBookings();

        return view('consoles.show', compact('console', 'currentBooking', 'upcomingBookings'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Console $console)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        return view('consoles.edit', compact('console'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Console $console)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|string|max:255',
            'hourly_rate' => 'required|numeric|min:0',
            'daily_rate' => 'required|numeric|min:0',
            'serial_number' => 'required|string|unique:consoles,serial_number,' . $console->id,
            'status' => 'required|in:available,rented,maintenance',
            'controller_count' => 'required|integer|min:1|max:4',
            'specifications' => 'nullable|array',
        ]);

        $console->update($request->all());

        return redirect()->route('admin.consoles.show', $console)
            ->with('success', 'Console updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Console $console)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        // Check if console has active bookings
        if ($console->bookings()->whereIn('status', ['active', 'confirmed'])->exists()) {
            return back()->withErrors(['console' => 'Cannot delete console with active bookings.']);
        }

        $console->delete();

        return redirect()->route('admin.consoles.index')
            ->with('success', 'Console deleted successfully!');
    }

    /**
     * Update console status
     */
    public function updateStatus(Request $request, Console $console)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'status' => 'required|in:available,rented,maintenance',
        ]);

        $console->update(['status' => $request->status]);

        return back()->with('success', 'Console status updated successfully!');
    }

    /**
     * Get available consoles for booking (API endpoint)
     */
    public function getAvailable(Request $request)
    {
        $startTime = $request->input('start_time');
        $endTime = $request->input('end_time');

        $availableConsoles = Console::where('status', 'available')
            ->whereDoesntHave('bookings', function ($query) use ($startTime, $endTime) {
                $query->where('status', '!=', 'cancelled')
                    ->where(function ($q) use ($startTime, $endTime) {
                        $q->whereBetween('start_time', [$startTime, $endTime])
                          ->orWhereBetween('end_time', [$startTime, $endTime])
                          ->orWhere(function ($subQ) use ($startTime, $endTime) {
                              $subQ->where('start_time', '<=', $startTime)
                                   ->where('end_time', '>=', $endTime);
                          });
                    });
            })
            ->get();

        return response()->json($availableConsoles);
    }
}
