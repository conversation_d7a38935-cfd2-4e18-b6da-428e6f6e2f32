<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>🎮 PlayStation Rental - Gaming Paradise</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=orbitron:400,500,600,700,800,900&family=exo-2:400,500,600,700&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Heroicons -->
        <script src="https://unpkg.com/@heroicons/react@2.0.18/24/outline/index.js" type="module"></script>

        <style>
            .gaming-font { font-family: 'Orbitron', monospace; }
            .content-font { font-family: 'Exo 2', sans-serif; }
            .gaming-bg {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                background-attachment: fixed;
            }
            .playstation-gradient {
                background: linear-gradient(135deg, #003087 0%, #0070f3 50%, #00d4ff 100%);
            }
            .gaming-card {
                backdrop-filter: blur(10px);
                background: rgba(255, 255, 255, 0.95);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            .glow-effect {
                box-shadow: 0 0 30px rgba(0, 112, 243, 0.3);
            }
            .pulse-glow {
                animation: pulse-glow 2s ease-in-out infinite alternate;
            }
            @keyframes pulse-glow {
                from { box-shadow: 0 0 20px rgba(0, 112, 243, 0.4); }
                to { box-shadow: 0 0 40px rgba(0, 112, 243, 0.8); }
            }
            .floating {
                animation: floating 3s ease-in-out infinite;
            }
            @keyframes floating {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-10px); }
            }
            .gaming-pattern {
                background-image:
                    radial-gradient(circle at 25% 25%, rgba(0, 112, 243, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
            }
            .neon-text {
                text-shadow: 0 0 10px rgba(0, 112, 243, 0.8), 0 0 20px rgba(0, 112, 243, 0.6), 0 0 30px rgba(0, 112, 243, 0.4);
            }
        </style>
    </head>
    <body class="content-font antialiased gaming-bg gaming-pattern">
        <div class="min-h-screen flex">
            <!-- Sidebar -->
            @include('layouts.sidebar')

            <!-- Main Content -->
            <div class="flex-1 flex flex-col overflow-hidden">
                <!-- Top Navigation -->
                @include('layouts.topnav')

                <!-- Page Content -->
                <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50">
                    <!-- Flash Messages -->
                    @if (session('success'))
                        <div class="mx-6 mt-6">
                            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                                <span class="block sm:inline">{{ session('success') }}</span>
                            </div>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="mx-6 mt-6">
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                                <span class="block sm:inline">{{ session('error') }}</span>
                            </div>
                        </div>
                    @endif

                    <!-- Page Heading -->
                    @isset($header)
                        <header class="bg-white shadow-sm border-b border-gray-200">
                            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                                {{ $header }}
                            </div>
                        </header>
                    @endisset

                    <!-- Main Content Area -->
                    <div class="container mx-auto px-6 py-8">
                        @yield('content')
                        {{ $slot ?? '' }}
                    </div>
                </main>
            </div>
        </div>

        <!-- Mobile menu overlay -->
        <div id="mobile-menu-overlay" class="fixed inset-0 z-40 bg-black bg-opacity-50 hidden lg:hidden"></div>

        <script>
            // Mobile menu toggle
            document.addEventListener('DOMContentLoaded', function() {
                const mobileMenuButton = document.getElementById('mobile-menu-button');
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('mobile-menu-overlay');

                if (mobileMenuButton) {
                    mobileMenuButton.addEventListener('click', function() {
                        sidebar.classList.toggle('-translate-x-full');
                        overlay.classList.toggle('hidden');
                    });
                }

                if (overlay) {
                    overlay.addEventListener('click', function() {
                        sidebar.classList.add('-translate-x-full');
                        overlay.classList.add('hidden');
                    });
                }
            });
        </script>
    </body>
</html>
