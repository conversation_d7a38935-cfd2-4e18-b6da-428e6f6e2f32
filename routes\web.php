<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\PostController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Redirect dashboard based on role
Route::get('/dashboard', function () {
    $user = auth()->user();
    if ($user->isAdmin()) {
        return redirect()->route('admin.dashboard');
    } else {
        return redirect()->route('user.dashboard');
    }
})->middleware(['auth', 'verified'])->name('dashboard');

// Admin routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'role:admin'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'adminDashboard'])->name('dashboard');

    // Rental Management
    Route::resource('bookings', BookingController::class);
    Route::post('/bookings/{booking}/confirm', [BookingController::class, 'confirm'])->name('bookings.confirm');
    Route::post('/bookings/{booking}/check-in', [BookingController::class, 'checkIn'])->name('bookings.check-in');
    Route::post('/bookings/{booking}/check-out', [BookingController::class, 'checkOut'])->name('bookings.check-out');

    Route::resource('consoles', ConsoleController::class);
    Route::post('/consoles/{console}/status', [ConsoleController::class, 'updateStatus'])->name('consoles.status');

    Route::resource('games', GameController::class);
    Route::get('/games/filter/platform', [GameController::class, 'filterByPlatform'])->name('games.filter');

    Route::resource('customers', CustomerController::class);
    Route::post('/customers/{customer}/membership', [CustomerController::class, 'updateMembership'])->name('customers.membership');

    // Reports
    Route::get('/reports', [ReportController::class, 'index'])->name('reports.index');
    Route::get('/reports/revenue', [ReportController::class, 'revenue'])->name('reports.revenue');
    Route::get('/reports/console-utilization', [ReportController::class, 'consoleUtilization'])->name('reports.console-utilization');
    Route::get('/reports/customers', [ReportController::class, 'customers'])->name('reports.customers');
    Route::get('/reports/export/revenue', [ReportController::class, 'exportRevenue'])->name('reports.export.revenue');

    // Blog Posts
    Route::resource('posts', PostController::class);
});

// User routes
Route::prefix('user')->name('user.')->middleware(['auth', 'role:user'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'userDashboard'])->name('dashboard');

    // User Rental
    Route::resource('bookings', BookingController::class)->except(['destroy']);
    Route::post('/bookings/{booking}/cancel', [BookingController::class, 'destroy'])->name('bookings.cancel');

    Route::get('/consoles', [ConsoleController::class, 'index'])->name('consoles.index');
    Route::get('/consoles/{console}', [ConsoleController::class, 'show'])->name('consoles.show');

    Route::get('/games', [GameController::class, 'index'])->name('games.index');
    Route::get('/games/{game}', [GameController::class, 'show'])->name('games.show');

    // Blog Posts
    Route::resource('posts', PostController::class);
});

// API routes for AJAX requests
Route::middleware('auth')->group(function () {
    Route::get('/api/consoles/available', [ConsoleController::class, 'getAvailable'])->name('api.consoles.available');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
