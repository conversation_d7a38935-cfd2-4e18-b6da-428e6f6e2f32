# Toko Rental PlayStation - Laravel 12 Project

Proyek Laravel 12 lengkap dengan sistem autentikasi berbasis role, CRUD Post, dan UI responsif menggunakan Tailwind CSS.

## 🚀 Fitur Utama

### Authentication & Role Management
- **<PERSON><PERSON> Breeze** dengan Blade views untuk sistem autentikasi
- **Role-based Access Control** dengan 2 role: `admin` dan `user`
- **Redirect otomatis** berdasarkan role setelah login:
  - Admin → `/admin/dashboard`
  - User → `/user/dashboard`
- **Middleware protection** untuk semua route berdasarkan role

### Dashboard Berbeda
- **Admin Dashboard**: Kelola semua user dan post, statistik sistem
- **User Dashboard**: <PERSON><PERSON><PERSON> post pribadi, statistik personal
- **Responsive Layout** dengan sidebar (kiri) dan navbar (atas)
- **Konsisten Design** dengan tema biru-putih

### CRUD Post Management
- **Admin**: Dapat mengakses dan mengedit semua post
- **User**: <PERSON><PERSON> dapat mengelola post miliknya sendiri
- **Fitur Lengkap**: Create, Read, Update, Delete
- **Form Validation** dengan feedback success/error
- **Pagination** untuk daftar post

### UI/UX Design
- **Tailwind CSS** untuk styling modern
- **Responsive Design** yang mobile-friendly
- **Heroicons** untuk ikon yang konsisten
- **Flash Messages** untuk notifikasi
- **Loading States** dan transisi smooth

## 📋 Persyaratan Sistem

- PHP 8.2 atau lebih tinggi
- Composer
- Node.js & NPM
- SQLite (default) atau MySQL/PostgreSQL

## 🛠️ Instalasi & Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd TokoRentalPlayStation
```

### 2. Install Dependencies
```bash
composer install
npm install
```

### 3. Environment Setup
```bash
cp .env.example .env
php artisan key:generate
```

### 4. Database Setup
```bash
php artisan migrate
php artisan db:seed
```

### 5. Build Assets
```bash
npm run build
# atau untuk development
npm run dev
```

### 6. Jalankan Server
```bash
php artisan serve
```

Aplikasi akan berjalan di `http://localhost:8000`

## 👥 Akun Demo

Setelah menjalankan seeder, Anda dapat login dengan akun berikut:

### Admin Account
- **Email**: `<EMAIL>`
- **Password**: `password`
- **Role**: `admin`

### User Account
- **Email**: `<EMAIL>`
- **Password**: `password`
- **Role**: `user`

### Additional Test Accounts
- **John Doe**: `<EMAIL>` / `password` (user)
- **Jane Smith**: `<EMAIL>` / `password` (user)

## 📁 Struktur Proyek

```
app/
├── Http/
│   ├── Controllers/
│   │   ├── DashboardController.php
│   │   └── PostController.php
│   └── Middleware/
│       └── RoleMiddleware.php
├── Models/
│   ├── User.php
│   └── Post.php
resources/
├── views/
│   ├── layouts/
│   │   ├── app.blade.php
│   │   ├── sidebar.blade.php
│   │   └── topnav.blade.php
│   ├── admin/
│   │   └── dashboard.blade.php
│   ├── user/
│   │   └── dashboard.blade.php
│   └── posts/
│       ├── index.blade.php
│       ├── create.blade.php
│       ├── edit.blade.php
│       └── show.blade.php
database/
├── migrations/
└── seeders/
    ├── UserSeeder.php
    └── PostSeeder.php
```

## 🔐 Role & Permissions

### Admin Role
- Akses ke semua fitur sistem
- Dapat melihat dan mengedit semua post
- Dashboard dengan statistik lengkap
- Route prefix: `/admin`

### User Role  
- Akses terbatas ke fitur personal
- Hanya dapat mengelola post sendiri
- Dashboard personal
- Route prefix: `/user`

## 🛡️ Security Features

- **CSRF Protection** pada semua form
- **Role-based Middleware** untuk akses kontrol
- **Input Validation** pada semua form
- **Password Hashing** dengan bcrypt
- **SQL Injection Protection** dengan Eloquent ORM

## 🎨 UI Components

### Layout Components
- **Responsive Sidebar** dengan navigasi role-based
- **Top Navigation** untuk mobile
- **Flash Messages** untuk feedback
- **Loading States** dan animasi

### Form Components
- **Styled Input Fields** dengan validation
- **Action Buttons** dengan loading states
- **Confirmation Dialogs** untuk aksi berbahaya
- **Rich Text Areas** untuk konten post

## 📱 Responsive Design

- **Mobile First** approach
- **Breakpoints**: sm, md, lg, xl
- **Touch-friendly** interface
- **Optimized** untuk semua device

## 🚀 Deployment

### Production Setup
1. Set `APP_ENV=production` di `.env`
2. Set `APP_DEBUG=false` di `.env`
3. Jalankan `php artisan config:cache`
4. Jalankan `php artisan route:cache`
5. Jalankan `php artisan view:cache`
6. Build assets: `npm run build`

### Server Requirements
- Web server (Apache/Nginx)
- PHP 8.2+ dengan extensions yang diperlukan
- Database server (MySQL/PostgreSQL/SQLite)
- Composer untuk dependency management

## 🤝 Contributing

1. Fork repository
2. Buat feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push ke branch (`git push origin feature/AmazingFeature`)
5. Buat Pull Request

## 📄 License

Proyek ini menggunakan [MIT License](https://opensource.org/licenses/MIT).

## 📞 Support

Jika Anda mengalami masalah atau memiliki pertanyaan, silakan buat issue di repository ini.

---

**Dibuat dengan ❤️ menggunakan Laravel 12, Tailwind CSS, dan Laravel Breeze**
