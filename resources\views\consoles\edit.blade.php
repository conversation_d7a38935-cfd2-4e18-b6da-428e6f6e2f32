@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Gaming Header -->
    <div class="bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-900 rounded-2xl shadow-xl p-8 text-white relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full floating"></div>
            <div class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full floating" style="animation-delay: 1s;"></div>
        </div>
        
        <div class="relative z-10 flex items-center justify-between">
            <div>
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-4 pulse-glow">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M21,6H3A1,1 0 0,0 2,7V17A1,1 0 0,0 3,18H21A1,1 0 0,0 22,17V7A1,1 0 0,0 21,6M20,16H4V8H20V16Z"/>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold gaming-font neon-text">Edit Console</h1>
                        <p class="text-purple-100 text-lg">{{ $console->name }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Console Form -->
    <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
        <form action="{{ route('admin.consoles.update', $console) }}" method="POST" class="space-y-6">
            @csrf
            @method('PUT')
            
            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Console Name *</label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           value="{{ old('name', $console->name) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror"
                           placeholder="e.g., PlayStation 5 #001"
                           required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Console Type *</label>
                    <select id="type" 
                            name="type" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('type') border-red-500 @enderror"
                            required>
                        <option value="">Select Console Type</option>
                        <option value="PlayStation 5" {{ old('type', $console->type) === 'PlayStation 5' ? 'selected' : '' }}>PlayStation 5</option>
                        <option value="PlayStation 4 Pro" {{ old('type', $console->type) === 'PlayStation 4 Pro' ? 'selected' : '' }}>PlayStation 4 Pro</option>
                        <option value="PlayStation 4 Slim" {{ old('type', $console->type) === 'PlayStation 4 Slim' ? 'selected' : '' }}>PlayStation 4 Slim</option>
                        <option value="PlayStation 4" {{ old('type', $console->type) === 'PlayStation 4' ? 'selected' : '' }}>PlayStation 4</option>
                    </select>
                    @error('type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="serial_number" class="block text-sm font-medium text-gray-700 mb-2">Serial Number *</label>
                    <input type="text" 
                           id="serial_number" 
                           name="serial_number" 
                           value="{{ old('serial_number', $console->serial_number) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('serial_number') border-red-500 @enderror"
                           placeholder="e.g., PS5-001-2024"
                           required>
                    @error('serial_number')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status *</label>
                    <select id="status" 
                            name="status" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('status') border-red-500 @enderror"
                            required>
                        <option value="available" {{ old('status', $console->status) === 'available' ? 'selected' : '' }}>Available</option>
                        <option value="maintenance" {{ old('status', $console->status) === 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                        <option value="rented" {{ old('status', $console->status) === 'rented' ? 'selected' : '' }}>Rented</option>
                    </select>
                    @error('status')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Pricing -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="hourly_rate" class="block text-sm font-medium text-gray-700 mb-2">Hourly Rate (Rp) *</label>
                    <input type="number" 
                           id="hourly_rate" 
                           name="hourly_rate" 
                           value="{{ old('hourly_rate', $console->hourly_rate) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('hourly_rate') border-red-500 @enderror"
                           placeholder="25000"
                           min="0"
                           step="1000"
                           required>
                    @error('hourly_rate')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="daily_rate" class="block text-sm font-medium text-gray-700 mb-2">Daily Rate (Rp) *</label>
                    <input type="number" 
                           id="daily_rate" 
                           name="daily_rate" 
                           value="{{ old('daily_rate', $console->daily_rate) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('daily_rate') border-red-500 @enderror"
                           placeholder="180000"
                           min="0"
                           step="1000"
                           required>
                    @error('daily_rate')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="controller_count" class="block text-sm font-medium text-gray-700 mb-2">Controller Count *</label>
                    <input type="number" 
                           id="controller_count" 
                           name="controller_count" 
                           value="{{ old('controller_count', $console->controller_count) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('controller_count') border-red-500 @enderror"
                           min="1"
                           max="4"
                           required>
                    @error('controller_count')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Specifications -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 gaming-font">Technical Specifications</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="cpu" class="block text-sm font-medium text-gray-700 mb-2">CPU</label>
                        <input type="text" 
                               id="cpu" 
                               name="specifications[cpu]" 
                               value="{{ old('specifications.cpu', $console->specifications['cpu'] ?? '') }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="e.g., AMD Zen 2 8-core">
                    </div>

                    <div>
                        <label for="gpu" class="block text-sm font-medium text-gray-700 mb-2">GPU</label>
                        <input type="text" 
                               id="gpu" 
                               name="specifications[gpu]" 
                               value="{{ old('specifications.gpu', $console->specifications['gpu'] ?? '') }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="e.g., AMD RDNA 2">
                    </div>

                    <div>
                        <label for="storage" class="block text-sm font-medium text-gray-700 mb-2">Storage</label>
                        <input type="text" 
                               id="storage" 
                               name="specifications[storage]" 
                               value="{{ old('specifications.storage', $console->specifications['storage'] ?? '') }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="e.g., 825GB SSD">
                    </div>

                    <div>
                        <label for="ram" class="block text-sm font-medium text-gray-700 mb-2">RAM</label>
                        <input type="text" 
                               id="ram" 
                               name="specifications[ram]" 
                               value="{{ old('specifications.ram', $console->specifications['ram'] ?? '') }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="e.g., 16GB GDDR6">
                    </div>

                    <div>
                        <label for="resolution" class="block text-sm font-medium text-gray-700 mb-2">Max Resolution</label>
                        <input type="text" 
                               id="resolution" 
                               name="specifications[resolution]" 
                               value="{{ old('specifications.resolution', $console->specifications['resolution'] ?? '') }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="e.g., 4K UHD">
                    </div>
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea id="description" 
                          name="description" 
                          rows="4"
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('description') border-red-500 @enderror"
                          placeholder="Describe the console features and condition...">{{ old('description', $console->description) }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('admin.consoles.index') }}" 
                       class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                        ← Back to List
                    </a>
                    
                    <a href="{{ route('admin.consoles.show', $console) }}" 
                       class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                        👁️ View Details
                    </a>
                </div>
                
                <div class="flex items-center space-x-4">
                    <form action="{{ route('admin.consoles.destroy', $console) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this console?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                class="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                            🗑️ Delete
                        </button>
                    </form>
                    
                    <button type="submit" 
                            class="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg">
                        <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z"/>
                        </svg>
                        Update Console
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Auto-calculate daily rate based on hourly rate
document.getElementById('hourly_rate').addEventListener('input', function() {
    const hourlyRate = parseInt(this.value) || 0;
    const dailyRate = hourlyRate * 7; // 7 hours discount
    document.getElementById('daily_rate').value = dailyRate;
});
</script>
@endsection
