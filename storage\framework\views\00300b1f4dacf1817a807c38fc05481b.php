<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Gaming Header -->
    <div class="bg-gradient-to-r from-orange-900 via-red-900 to-pink-900 rounded-2xl shadow-xl p-8 text-white relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full floating"></div>
            <div class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full floating" style="animation-delay: 1s;"></div>
        </div>
        
        <div class="relative z-10 flex items-center justify-between">
            <div>
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-4 pulse-glow">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,15H13V17H11V15M11,7H13V13H11V7Z"/>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold gaming-font neon-text">Notifications & Alerts</h1>
                        <p class="text-orange-100 text-lg">Real-time system notifications</p>
                    </div>
                </div>
            </div>
            <div class="text-right">
                <button onclick="markAllAsRead()" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 border border-white/30">
                    <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9,12L11,14L15,10M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2Z"/>
                    </svg>
                    Mark All Read
                </button>
            </div>
        </div>
    </div>

    <!-- Notification Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="gaming-card rounded-xl shadow-sm p-6 border-l-4 border-red-500 glow-effect">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100">
                    <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M11,15H13V17H11V15M11,7H13V13H11V7M12,2C6.47,2 2,6.5 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Urgent</p>
                    <p class="text-2xl font-bold text-gray-900" id="urgent-count"><?php echo e(collect($notifications)->where('priority', 'urgent')->count()); ?></p>
                </div>
            </div>
        </div>
        
        <div class="gaming-card rounded-xl shadow-sm p-6 border-l-4 border-orange-500 glow-effect">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100">
                    <svg class="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6Z"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">High Priority</p>
                    <p class="text-2xl font-bold text-gray-900" id="high-count"><?php echo e(collect($notifications)->where('priority', 'high')->count()); ?></p>
                </div>
            </div>
        </div>
        
        <div class="gaming-card rounded-xl shadow-sm p-6 border-l-4 border-yellow-500 glow-effect">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6Z"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Medium Priority</p>
                    <p class="text-2xl font-bold text-gray-900" id="medium-count"><?php echo e(collect($notifications)->where('priority', 'medium')->count()); ?></p>
                </div>
            </div>
        </div>
        
        <div class="gaming-card rounded-xl shadow-sm p-6 border-l-4 border-blue-500 glow-effect">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2Z"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total</p>
                    <p class="text-2xl font-bold text-gray-900" id="total-count"><?php echo e(count($notifications)); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="gaming-card rounded-xl shadow-lg glow-effect">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900 gaming-font">All Notifications</h2>
        </div>
        
        <div id="notifications-container" class="divide-y divide-gray-200">
            <?php $__empty_1 = true; $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="notification-item p-6 hover:bg-gray-50 transition-colors duration-200 <?php echo e(!$notification['read'] ? 'bg-blue-50' : ''); ?>" 
                     data-notification-id="<?php echo e($notification['id']); ?>" 
                     data-priority="<?php echo e($notification['priority']); ?>">
                    <div class="flex items-start">
                        <!-- Priority Icon -->
                        <div class="flex-shrink-0 mr-4">
                            <?php if($notification['priority'] === 'urgent'): ?>
                                <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center pulse-glow">
                                    <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M11,15H13V17H11V15M11,7H13V13H11V7M12,2C6.47,2 2,6.5 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                                    </svg>
                                </div>
                            <?php elseif($notification['priority'] === 'high'): ?>
                                <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-orange-600" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6Z"/>
                                    </svg>
                                </div>
                            <?php elseif($notification['priority'] === 'medium'): ?>
                                <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6Z"/>
                                    </svg>
                                </div>
                            <?php else: ?>
                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2Z"/>
                                    </svg>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Notification Content -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-sm font-semibold text-gray-900"><?php echo e($notification['title']); ?></h3>
                                <div class="flex items-center space-x-2">
                                    <!-- Priority Badge -->
                                    <?php if($notification['priority'] === 'urgent'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            🚨 Urgent
                                        </span>
                                    <?php elseif($notification['priority'] === 'high'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                            ⚠️ High
                                        </span>
                                    <?php elseif($notification['priority'] === 'medium'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            📋 Medium
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            ℹ️ Info
                                        </span>
                                    <?php endif; ?>
                                    
                                    <!-- Time -->
                                    <span class="text-xs text-gray-500"><?php echo e($notification['time']->diffForHumans()); ?></span>
                                </div>
                            </div>
                            
                            <p class="text-sm text-gray-700 mb-3"><?php echo e($notification['message']); ?></p>
                            
                            <!-- Action Buttons -->
                            <div class="flex items-center space-x-3">
                                <?php if(isset($notification['booking_id'])): ?>
                                    <a href="<?php echo e(route('admin.bookings.show', $notification['booking_id'])); ?>" 
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        View Booking →
                                    </a>
                                <?php endif; ?>
                                
                                <?php if(isset($notification['console_id'])): ?>
                                    <a href="<?php echo e(route('admin.consoles.show', $notification['console_id'])); ?>" 
                                       class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                        View Console →
                                    </a>
                                <?php endif; ?>
                                
                                <?php if(!$notification['read']): ?>
                                    <button onclick="markAsRead('<?php echo e($notification['id']); ?>')" 
                                            class="text-green-600 hover:text-green-800 text-sm font-medium">
                                        Mark as Read
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="text-center py-12">
                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,15H13V17H11V15M11,7H13V13H11V7Z"/>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No notifications</h3>
                    <p class="text-gray-500">All caught up! No new notifications at this time.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Auto-refresh notifications every 30 seconds
setInterval(refreshNotifications, 30000);

function refreshNotifications() {
    fetch('/admin/notifications/api')
        .then(response => response.json())
        .then(data => {
            updateNotificationCounts(data.notifications);
            // Update notification badge in navbar if exists
            const badge = document.getElementById('notification-badge');
            if (badge) {
                badge.textContent = data.unread_count;
                badge.style.display = data.unread_count > 0 ? 'block' : 'none';
            }
        })
        .catch(error => console.error('Error refreshing notifications:', error));
}

function updateNotificationCounts(notifications) {
    const counts = {
        urgent: notifications.filter(n => n.priority === 'urgent').length,
        high: notifications.filter(n => n.priority === 'high').length,
        medium: notifications.filter(n => n.priority === 'medium').length,
        total: notifications.length
    };
    
    document.getElementById('urgent-count').textContent = counts.urgent;
    document.getElementById('high-count').textContent = counts.high;
    document.getElementById('medium-count').textContent = counts.medium;
    document.getElementById('total-count').textContent = counts.total;
}

function markAsRead(notificationId) {
    fetch('/admin/notifications/mark-read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ notification_id: notificationId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const item = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (item) {
                item.classList.remove('bg-blue-50');
                item.querySelector('button[onclick*="markAsRead"]')?.remove();
            }
            refreshNotifications();
        }
    })
    .catch(error => console.error('Error marking notification as read:', error));
}

function markAllAsRead() {
    fetch('/admin/notifications/mark-all-read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.querySelectorAll('.notification-item').forEach(item => {
                item.classList.remove('bg-blue-50');
            });
            document.querySelectorAll('button[onclick*="markAsRead"]').forEach(btn => btn.remove());
            refreshNotifications();
        }
    })
    .catch(error => console.error('Error marking all notifications as read:', error));
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\TokoRentalPlayStation\resources\views/admin/notifications/index.blade.php ENDPATH**/ ?>