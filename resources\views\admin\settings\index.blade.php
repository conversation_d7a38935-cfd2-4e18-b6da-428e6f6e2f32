@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-900 rounded-2xl shadow-xl p-8 text-white relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full floating"></div>
            <div class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full floating" style="animation-delay: 1s;"></div>
        </div>

        <div class="relative z-10 flex items-center justify-between">
            <div>
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-4 pulse-glow">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold gaming-font neon-text">Store Settings</h1>
                        <p class="text-purple-100 text-lg">Configure your PlayStation rental store</p>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <button onclick="exportSettings()" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 border border-white/30">
                    <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    Export Settings
                </button>
                <form action="{{ route('admin.settings.reset') }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to reset all settings to defaults?')">
                    @csrf
                    <button type="submit" class="bg-red-500/20 hover:bg-red-500/30 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 border border-red-300/30">
                        <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12,4C14.1,4.1 15.9,5.1 17.2,6.6C17.8,7.3 18.3,8.1 18.6,9H16.6C16.2,8.1 15.6,7.4 14.9,6.9C13.7,6.1 12.4,5.9 11.1,6.3C9.8,6.7 8.7,7.6 8,8.9C7.3,10.2 7.1,11.7 7.5,13.1C7.9,14.5 8.8,15.6 10,16.3C11.2,17 12.6,17.2 14,16.8C15.4,16.4 16.5,15.5 17.2,14.2H19.2C18.5,16.3 16.8,17.9 14.7,18.7C12.6,19.5 10.3,19.3 8.4,18.2C6.5,17.1 5.2,15.2 4.8,13C4.4,10.8 5,8.6 6.4,6.9C7.8,5.2 9.8,4.1 12,4M19,8V11H16L20.5,15.5L22,14L19,11H22V8H19Z"/>
                        </svg>
                        Reset to Defaults
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Settings Tabs -->
    <div class="gaming-card rounded-xl shadow-lg glow-effect">
        <div class="border-b border-gray-200">
            <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button onclick="showTab('general')" class="tab-button active py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600" data-tab="general">
                    🏪 General
                </button>
                <button onclick="showTab('hours')" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="hours">
                    🕒 Operating Hours
                </button>
                <button onclick="showTab('pricing')" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="pricing">
                    💰 Pricing & Fees
                </button>
                <button onclick="showTab('booking')" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="booking">
                    📅 Booking Rules
                </button>
                <button onclick="showTab('payment')" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="payment">
                    💳 Payment Methods
                </button>
                <button onclick="showTab('notifications')" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="notifications">
                    🔔 Notifications
                </button>
            </nav>
        </div>

        <form action="{{ route('admin.settings.update') }}" method="POST" class="p-6">
            @csrf
            @method('PUT')

            <!-- General Settings Tab -->
            <div id="general-tab" class="tab-content">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 gaming-font">🏪 General Store Information</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="store_name" class="block text-sm font-medium text-gray-700 mb-2">Store Name</label>
                        <input type="text"
                               id="store_name"
                               name="store_name"
                               value="{{ old('store_name', $settings['store_name']) }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('store_name') border-red-500 @enderror"
                               required>
                        @error('store_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="store_email" class="block text-sm font-medium text-gray-700 mb-2">Store Email</label>
                        <input type="email"
                               id="store_email"
                               name="store_email"
                               value="{{ old('store_email', $settings['store_email']) }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('store_email') border-red-500 @enderror">
                        @error('store_email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="store_phone" class="block text-sm font-medium text-gray-700 mb-2">Store Phone</label>
                        <input type="text"
                               id="store_phone"
                               name="store_phone"
                               value="{{ old('store_phone', $settings['store_phone']) }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('store_phone') border-red-500 @enderror">
                        @error('store_phone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="currency" class="block text-sm font-medium text-gray-700 mb-2">Currency</label>
                        <select id="currency"
                                name="currency"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('currency') border-red-500 @enderror"
                                required>
                            <option value="IDR" {{ old('currency', $settings['currency']) === 'IDR' ? 'selected' : '' }}>IDR - Indonesian Rupiah</option>
                            <option value="USD" {{ old('currency', $settings['currency']) === 'USD' ? 'selected' : '' }}>USD - US Dollar</option>
                            <option value="EUR" {{ old('currency', $settings['currency']) === 'EUR' ? 'selected' : '' }}>EUR - Euro</option>
                            <option value="MYR" {{ old('currency', $settings['currency']) === 'MYR' ? 'selected' : '' }}>MYR - Malaysian Ringgit</option>
                        </select>
                        @error('currency')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="mt-6">
                    <label for="store_description" class="block text-sm font-medium text-gray-700 mb-2">Store Description</label>
                    <textarea id="store_description"
                              name="store_description"
                              rows="3"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('store_description') border-red-500 @enderror"
                              placeholder="Describe your PlayStation rental store...">{{ old('store_description', $settings['store_description']) }}</textarea>
                    @error('store_description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="mt-6">
                    <label for="store_address" class="block text-sm font-medium text-gray-700 mb-2">Store Address</label>
                    <textarea id="store_address"
                              name="store_address"
                              rows="3"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('store_address') border-red-500 @enderror"
                              placeholder="Enter your store address...">{{ old('store_address', $settings['store_address']) }}</textarea>
                    @error('store_address')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Maintenance Mode -->
                <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <input type="checkbox"
                               id="maintenance_mode"
                               name="maintenance_mode"
                               value="1"
                               {{ old('maintenance_mode', $settings['maintenance_mode']) ? 'checked' : '' }}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="maintenance_mode" class="ml-3 text-sm font-medium text-gray-700">
                            🔧 Enable Maintenance Mode
                        </label>
                    </div>
                    <p class="mt-2 text-sm text-gray-600">When enabled, the store will be closed for new bookings and customers will see a maintenance message.</p>
                </div>
            </div>

            <!-- Operating Hours Tab -->
            <div id="hours-tab" class="tab-content hidden">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 gaming-font">🕒 Operating Hours</h3>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="opening_time" class="block text-sm font-medium text-gray-700 mb-2">Opening Time</label>
                        <input type="time"
                               id="opening_time"
                               name="opening_time"
                               value="{{ old('opening_time', $settings['opening_time']) }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('opening_time') border-red-500 @enderror"
                               required>
                        @error('opening_time')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="closing_time" class="block text-sm font-medium text-gray-700 mb-2">Closing Time</label>
                        <input type="time"
                               id="closing_time"
                               name="closing_time"
                               value="{{ old('closing_time', $settings['closing_time']) }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('closing_time') border-red-500 @enderror"
                               required>
                        @error('closing_time')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                        <select id="timezone"
                                name="timezone"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('timezone') border-red-500 @enderror"
                                required>
                            <option value="Asia/Jakarta" {{ old('timezone', $settings['timezone']) === 'Asia/Jakarta' ? 'selected' : '' }}>Asia/Jakarta (WIB)</option>
                            <option value="Asia/Makassar" {{ old('timezone', $settings['timezone']) === 'Asia/Makassar' ? 'selected' : '' }}>Asia/Makassar (WITA)</option>
                            <option value="Asia/Jayapura" {{ old('timezone', $settings['timezone']) === 'Asia/Jayapura' ? 'selected' : '' }}>Asia/Jayapura (WIT)</option>
                            <option value="Asia/Kuala_Lumpur" {{ old('timezone', $settings['timezone']) === 'Asia/Kuala_Lumpur' ? 'selected' : '' }}>Asia/Kuala_Lumpur (MYT)</option>
                            <option value="Asia/Singapore" {{ old('timezone', $settings['timezone']) === 'Asia/Singapore' ? 'selected' : '' }}>Asia/Singapore (SGT)</option>
                        </select>
                        @error('timezone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-900 mb-2">Current Store Status</h4>
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full {{ \App\Http\Controllers\SettingsController::isStoreOpen() ? 'bg-green-500' : 'bg-red-500' }} mr-2"></div>
                            <span class="text-sm font-medium {{ \App\Http\Controllers\SettingsController::isStoreOpen() ? 'text-green-700' : 'text-red-700' }}">
                                {{ \App\Http\Controllers\SettingsController::isStoreOpen() ? 'Store is Open' : 'Store is Closed' }}
                            </span>
                        </div>
                        <span class="text-sm text-gray-600">Current time: {{ now()->format('H:i') }}</span>
                    </div>
                </div>
            </div>

            <!-- Pricing & Fees Tab -->
            <div id="pricing-tab" class="tab-content hidden">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 gaming-font">💰 Pricing & Fees</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="tax_rate" class="block text-sm font-medium text-gray-700 mb-2">Tax Rate (%)</label>
                        <input type="number"
                               id="tax_rate"
                               name="tax_rate"
                               value="{{ old('tax_rate', $settings['tax_rate']) }}"
                               min="0"
                               max="100"
                               step="0.01"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('tax_rate') border-red-500 @enderror">
                        @error('tax_rate')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="late_fee_rate" class="block text-sm font-medium text-gray-700 mb-2">Late Fee (per hour)</label>
                        <input type="number"
                               id="late_fee_rate"
                               name="late_fee_rate"
                               value="{{ old('late_fee_rate', $settings['late_fee_rate']) }}"
                               min="0"
                               step="1000"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('late_fee_rate') border-red-500 @enderror">
                        @error('late_fee_rate')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="deposit_percentage" class="block text-sm font-medium text-gray-700 mb-2">Deposit Percentage (%)</label>
                        <input type="number"
                               id="deposit_percentage"
                               name="deposit_percentage"
                               value="{{ old('deposit_percentage', $settings['deposit_percentage']) }}"
                               min="0"
                               max="100"
                               step="5"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('deposit_percentage') border-red-500 @enderror">
                        @error('deposit_percentage')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox"
                               id="require_deposit"
                               name="require_deposit"
                               value="1"
                               {{ old('require_deposit', $settings['require_deposit']) ? 'checked' : '' }}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="require_deposit" class="ml-3 text-sm font-medium text-gray-700">
                            Require Deposit for All Bookings
                        </label>
                    </div>
                </div>
            </div>

            <!-- Booking Rules Tab -->
            <div id="booking-tab" class="tab-content hidden">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 gaming-font">📅 Booking Rules</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="booking_advance_days" class="block text-sm font-medium text-gray-700 mb-2">Max Advance Booking (days)</label>
                        <input type="number"
                               id="booking_advance_days"
                               name="booking_advance_days"
                               value="{{ old('booking_advance_days', $settings['booking_advance_days']) }}"
                               min="1"
                               max="365"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('booking_advance_days') border-red-500 @enderror"
                               required>
                        @error('booking_advance_days')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="max_booking_duration" class="block text-sm font-medium text-gray-700 mb-2">Max Booking Duration (hours)</label>
                        <input type="number"
                               id="max_booking_duration"
                               name="max_booking_duration"
                               value="{{ old('max_booking_duration', $settings['max_booking_duration']) }}"
                               min="1"
                               max="168"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('max_booking_duration') border-red-500 @enderror"
                               required>
                        @error('max_booking_duration')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="mt-6 space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox"
                               id="auto_confirm_bookings"
                               name="auto_confirm_bookings"
                               value="1"
                               {{ old('auto_confirm_bookings', $settings['auto_confirm_bookings']) ? 'checked' : '' }}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="auto_confirm_bookings" class="ml-3 text-sm font-medium text-gray-700">
                            Auto-confirm Bookings
                        </label>
                    </div>
                    <p class="text-sm text-gray-600 ml-7">When enabled, bookings will be automatically confirmed without admin approval.</p>

                    <div class="flex items-center">
                        <input type="checkbox"
                               id="allow_online_payment"
                               name="allow_online_payment"
                               value="1"
                               {{ old('allow_online_payment', $settings['allow_online_payment']) ? 'checked' : '' }}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="allow_online_payment" class="ml-3 text-sm font-medium text-gray-700">
                            Allow Online Payment
                        </label>
                    </div>
                    <p class="text-sm text-gray-600 ml-7">Enable customers to pay online during booking process.</p>
                </div>
            </div>

            <!-- Payment Methods Tab -->
            <div id="payment-tab" class="tab-content hidden">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 gaming-font">💳 Payment Methods</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @php
                        $paymentMethods = [
                            'cash' => ['name' => 'Cash', 'icon' => '💵'],
                            'bank_transfer' => ['name' => 'Bank Transfer', 'icon' => '🏦'],
                            'credit_card' => ['name' => 'Credit Card', 'icon' => '💳'],
                            'debit_card' => ['name' => 'Debit Card', 'icon' => '💳'],
                            'e_wallet' => ['name' => 'E-Wallet (OVO, GoPay, DANA)', 'icon' => '📱'],
                            'crypto' => ['name' => 'Cryptocurrency', 'icon' => '₿']
                        ];
                        $selectedMethods = old('payment_methods', $settings['payment_methods'] ?? []);
                    @endphp

                    @foreach($paymentMethods as $key => $method)
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <input type="checkbox"
                                   id="payment_{{ $key }}"
                                   name="payment_methods[]"
                                   value="{{ $key }}"
                                   {{ in_array($key, $selectedMethods) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="payment_{{ $key }}" class="ml-3 text-sm font-medium text-gray-700 flex items-center">
                                <span class="text-lg mr-2">{{ $method['icon'] }}</span>
                                {{ $method['name'] }}
                            </label>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Notifications Tab -->
            <div id="notifications-tab" class="tab-content hidden">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 gaming-font">🔔 Notification Settings</h3>

                <div class="space-y-6">
                    <div>
                        <label for="reminder_hours_before" class="block text-sm font-medium text-gray-700 mb-2">Send Reminder (hours before)</label>
                        <input type="number"
                               id="reminder_hours_before"
                               name="reminder_hours_before"
                               value="{{ old('reminder_hours_before', $settings['reminder_hours_before']) }}"
                               min="1"
                               max="72"
                               class="w-full md:w-1/3 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('reminder_hours_before') border-red-500 @enderror"
                               required>
                        @error('reminder_hours_before')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="notification_email"
                                   name="notification_email"
                                   value="1"
                                   {{ old('notification_email', $settings['notification_email']) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="notification_email" class="ml-3 text-sm font-medium text-gray-700">
                                📧 Enable Email Notifications
                            </label>
                        </div>
                        <p class="text-sm text-gray-600 ml-7">Send booking confirmations, reminders, and updates via email.</p>

                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="notification_sms"
                                   name="notification_sms"
                                   value="1"
                                   {{ old('notification_sms', $settings['notification_sms']) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="notification_sms" class="ml-3 text-sm font-medium text-gray-700">
                                📱 Enable SMS Notifications
                            </label>
                        </div>
                        <p class="text-sm text-gray-600 ml-7">Send booking reminders and important updates via SMS.</p>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="mt-8 flex items-center justify-between pt-6 border-t border-gray-200">
                <div class="text-sm text-gray-600">
                    Last updated: {{ \Carbon\Carbon::now()->format('d M Y, H:i') }}
                </div>
                <button type="submit"
                        class="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg">
                    <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z"/>
                    </svg>
                    Save Settings
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Tab functionality
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.add('hidden');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active', 'border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500');
    });

    // Show selected tab content
    document.getElementById(tabName + '-tab').classList.remove('hidden');

    // Add active class to selected tab button
    const activeButton = document.querySelector(`[data-tab="${tabName}"]`);
    activeButton.classList.add('active', 'border-blue-500', 'text-blue-600');
    activeButton.classList.remove('border-transparent', 'text-gray-500');
}

// Export settings
function exportSettings() {
    fetch('{{ route("admin.settings.export") }}')
        .then(response => response.json())
        .then(data => {
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `store_settings_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        })
        .catch(error => {
            console.error('Error exporting settings:', error);
            alert('Failed to export settings. Please try again.');
        });
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const openingTime = document.getElementById('opening_time');
    const closingTime = document.getElementById('closing_time');

    function validateTimes() {
        if (openingTime.value && closingTime.value) {
            const opening = new Date('2000-01-01 ' + openingTime.value);
            const closing = new Date('2000-01-01 ' + closingTime.value);

            if (opening >= closing) {
                closingTime.setCustomValidity('Closing time must be after opening time');
            } else {
                closingTime.setCustomValidity('');
            }
        }
    }

    openingTime.addEventListener('change', validateTimes);
    closingTime.addEventListener('change', validateTimes);
});
</script>
@endsection