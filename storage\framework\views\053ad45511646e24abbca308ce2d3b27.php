<!-- Gaming Sidebar -->
<div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 gaming-card shadow-2xl transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 glow-effect">
    <div class="flex items-center justify-center h-20 playstation-gradient relative overflow-hidden">
        <div class="absolute inset-0 opacity-20">
            <div class="absolute top-2 right-2 w-8 h-8 bg-white rounded-full floating"></div>
            <div class="absolute bottom-2 left-2 w-6 h-6 bg-white rounded-full floating" style="animation-delay: 1s;"></div>
        </div>
        <div class="relative z-10 flex items-center">
            <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mr-3 pulse-glow">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
            </div>
            <div>
                <h1 class="text-xl font-bold text-white gaming-font neon-text">PlayStation</h1>
                <p class="text-xs text-blue-100">Rental Paradise</p>
            </div>
        </div>
    </div>

    <nav class="mt-8">
        <div class="px-4 space-y-2">
            <!-- Dashboard -->
            <?php if(auth()->user()->isAdmin()): ?>
                <a href="<?php echo e(route('admin.dashboard')); ?>" 
                   class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('admin.dashboard') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : ''); ?>">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                    </svg>
                    Dashboard
                </a>
            <?php else: ?>
                <a href="<?php echo e(route('user.dashboard')); ?>" 
                   class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('user.dashboard') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : ''); ?>">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                    </svg>
                    Dashboard
                </a>
            <?php endif; ?>

            <?php if(auth()->user()->isAdmin()): ?>
                <!-- Admin Menu -->
                <!-- Bookings Management -->
                <a href="<?php echo e(route('admin.bookings.index')); ?>"
                   class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('admin.bookings.*') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : ''); ?>">
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
                    </svg>
                    Bookings
                </a>

                <!-- Console Management -->
                <a href="<?php echo e(route('admin.consoles.index')); ?>"
                   class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('admin.consoles.*') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : ''); ?>">
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M21,6H3A1,1 0 0,0 2,7V17A1,1 0 0,0 3,18H21A1,1 0 0,0 22,17V7A1,1 0 0,0 21,6M20,16H4V8H20V16Z"/>
                    </svg>
                    Consoles
                </a>

                <!-- Game Library -->
                <a href="<?php echo e(route('admin.games.index')); ?>"
                   class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('admin.games.*') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : ''); ?>">
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M7.5,4A5.5,5.5 0 0,0 2,9.5C2,10 2.04,10.5 2.12,11H6.5L7.66,8.5H8.34L9.5,11H13.88C13.96,10.5 14,10 14,9.5A5.5,5.5 0 0,0 8.5,4H7.5M21.8,12H2.2L1.27,21H22.73L21.8,12Z"/>
                    </svg>
                    Games
                </a>

                <!-- Customer Management -->
                <a href="<?php echo e(route('admin.customers.index')); ?>"
                   class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('admin.customers.*') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : ''); ?>">
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M16,4C18.11,4 19.8,5.69 19.8,7.8C19.8,9.91 18.11,11.6 16,11.6C13.89,11.6 12.2,9.91 12.2,7.8C12.2,5.69 13.89,4 16,4M16,13.4C18.67,13.4 24,14.73 24,17.4V20H8V17.4C8,14.73 13.33,13.4 16,13.4M8.8,13.4H6C3.33,13.4 0,14.73 0,17.4V20H6V17.4C6,15.77 6.67,14.6 8.8,13.4M6,4C8.11,4 9.8,5.69 9.8,7.8C9.8,9.91 8.11,11.6 6,11.6C3.89,11.6 2.2,9.91 2.2,7.8C2.2,5.69 3.89,4 6,4Z"/>
                    </svg>
                    Customers
                </a>

                <!-- Reports -->
                <a href="<?php echo e(route('admin.reports.index')); ?>"
                   class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('admin.reports.*') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : ''); ?>">
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M9,17H7V10H9V17M13,17H11V7H13V17M17,17H15V13H17V17Z"/>
                    </svg>
                    Reports
                </a>

                <!-- Posts -->
                <a href="<?php echo e(route('admin.posts.index')); ?>"
                   class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('admin.posts.*') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : ''); ?>">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                    </svg>
                    Blog Posts
                </a>
            <?php else: ?>
                <!-- User Menu -->
                <!-- Book Console -->
                <a href="<?php echo e(route('user.bookings.create')); ?>"
                   class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200">
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                    </svg>
                    Book Console
                </a>

                <!-- My Bookings -->
                <a href="<?php echo e(route('user.bookings.index')); ?>"
                   class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('user.bookings.*') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : ''); ?>">
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
                    </svg>
                    My Bookings
                </a>

                <!-- Browse Games -->
                <a href="<?php echo e(route('user.games.index')); ?>"
                   class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('user.games.*') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : ''); ?>">
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M7.5,4A5.5,5.5 0 0,0 2,9.5C2,10 2.04,10.5 2.12,11H6.5L7.66,8.5H8.34L9.5,11H13.88C13.96,10.5 14,10 14,9.5A5.5,5.5 0 0,0 8.5,4H7.5M21.8,12H2.2L1.27,21H22.73L21.8,12Z"/>
                    </svg>
                    Browse Games
                </a>

                <!-- My Posts -->
                <a href="<?php echo e(route('user.posts.index')); ?>"
                   class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('user.posts.*') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : ''); ?>">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                    </svg>
                    My Posts
                </a>
            <?php endif; ?>

            <!-- Profile -->
            <a href="<?php echo e(route('profile.edit')); ?>" 
               class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('profile.*') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : ''); ?>">
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                Profile
            </a>
        </div>

        <!-- User Info & Logout -->
        <div class="absolute bottom-0 w-full p-4 border-t border-gray-200">
            <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                    <span class="text-white text-sm font-medium"><?php echo e(substr(auth()->user()->name, 0, 1)); ?></span>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-700"><?php echo e(auth()->user()->name); ?></p>
                    <p class="text-xs text-gray-500 capitalize"><?php echo e(auth()->user()->role); ?></p>
                </div>
            </div>
            
            <form method="POST" action="<?php echo e(route('logout')); ?>">
                <?php echo csrf_field(); ?>
                <button type="submit" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 rounded-lg hover:bg-red-50 hover:text-red-600 transition-colors duration-200">
                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                    </svg>
                    Logout
                </button>
            </form>
        </div>
    </nav>
</div>
<?php /**PATH C:\Users\<USER>\TokoRentalPlayStation\resources\views/layouts/sidebar.blade.php ENDPATH**/ ?>