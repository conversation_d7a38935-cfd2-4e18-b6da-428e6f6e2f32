<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Gaming Header -->
    <div class="bg-gradient-to-r from-blue-900 via-indigo-900 to-purple-900 rounded-2xl shadow-xl p-8 text-white relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full floating"></div>
            <div class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full floating" style="animation-delay: 1s;"></div>
        </div>
        
        <div class="relative z-10 flex items-center justify-between">
            <div>
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-4 pulse-glow">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M21,6H3A1,1 0 0,0 2,7V17A1,1 0 0,0 3,18H21A1,1 0 0,0 22,17V7A1,1 0 0,0 21,6M20,16H4V8H20V16Z"/>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold gaming-font neon-text"><?php echo e($console->name); ?></h1>
                        <p class="text-blue-100 text-lg"><?php echo e($console->type); ?></p>
                    </div>
                </div>
            </div>
            <div class="text-right">
                <?php if($console->status === 'available'): ?>
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium bg-green-100 text-green-800 pulse-glow">
                        🎮 Available
                    </span>
                <?php elseif($console->status === 'rented'): ?>
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium bg-blue-100 text-blue-800">
                        🔥 Rented
                    </span>
                <?php else: ?>
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium bg-yellow-100 text-yellow-800">
                        🔧 Maintenance
                    </span>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Console Details -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Console Information -->
        <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
            <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
                <svg class="w-6 h-6 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M11,15H13V17H11V15M11,7H13V13H11V7M12,2C6.47,2 2,6.5 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                </svg>
                Console Information
            </h2>
            
            <!-- Basic Info -->
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 mb-6 border border-blue-200">
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center">
                        <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3 floating">
                            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M21,6H3A1,1 0 0,0 2,7V17A1,1 0 0,0 3,18H21A1,1 0 0,0 22,17V7A1,1 0 0,0 21,6M20,16H4V8H20V16Z"/>
                            </svg>
                        </div>
                        <h3 class="font-bold text-gray-900 text-lg"><?php echo e($console->name); ?></h3>
                        <p class="text-gray-600"><?php echo e($console->type); ?></p>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Serial Number:</span>
                            <span class="font-medium text-blue-600"><?php echo e($console->serial_number); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Controllers:</span>
                            <span class="font-medium"><?php echo e($console->controller_count); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Total Bookings:</span>
                            <span class="font-medium"><?php echo e($console->total_bookings ?? 0); ?></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Pricing -->
            <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 border border-green-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z"/>
                    </svg>
                    Pricing
                </h3>
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-4 bg-white rounded-lg border border-green-300">
                        <p class="text-sm text-gray-600 mb-1">Hourly Rate</p>
                        <p class="text-2xl font-bold text-green-600 neon-text">Rp <?php echo e(number_format($console->hourly_rate)); ?></p>
                    </div>
                    <div class="text-center p-4 bg-white rounded-lg border border-blue-300">
                        <p class="text-sm text-gray-600 mb-1">Daily Rate</p>
                        <p class="text-2xl font-bold text-blue-600 neon-text">Rp <?php echo e(number_format($console->daily_rate)); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Specifications & Actions -->
        <div class="space-y-6">
            <!-- Specifications -->
            <?php if($console->specifications): ?>
                <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
                    <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
                        <svg class="w-6 h-6 mr-2 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6Z"/>
                        </svg>
                        Technical Specifications
                    </h2>
                    
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-200">
                        <div class="grid grid-cols-1 gap-3">
                            <?php $__currentLoopData = $console->specifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex justify-between items-center p-3 bg-white rounded-lg border border-purple-100">
                                    <span class="text-gray-600 font-medium"><?php echo e(ucfirst(str_replace('_', ' ', $key))); ?>:</span>
                                    <span class="font-bold text-purple-600"><?php echo e($value); ?></span>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Actions -->
            <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
                <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
                    <svg class="w-6 h-6 mr-2 text-orange-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21V20A2,2 0 0,1 19,22H5A2,2 0 0,1 3,20V19H2A1,1 0 0,1 1,18V15A1,1 0 0,1 2,14H3A7,7 0 0,1 10,7H11V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2M7.5,13A2.5,2.5 0 0,0 5,15.5A2.5,2.5 0 0,0 7.5,18A2.5,2.5 0 0,0 10,15.5A2.5,2.5 0 0,0 7.5,13M16.5,13A2.5,2.5 0 0,0 14,15.5A2.5,2.5 0 0,0 16.5,18A2.5,2.5 0 0,0 19,15.5A2.5,2.5 0 0,0 16.5,13Z"/>
                    </svg>
                    Actions
                </h2>
                
                <div class="space-y-4">
                    <?php if(!auth()->user()->isAdmin() && $console->status === 'available'): ?>
                        <a href="<?php echo e(route('user.bookings.create', ['console_id' => $console->id])); ?>" class="block w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-lg font-semibold text-center transition-all duration-200 transform hover:scale-105 pulse-glow">
                            🎮 Book This Console
                        </a>
                    <?php endif; ?>
                    
                    <?php if(auth()->user()->isAdmin()): ?>
                        <a href="<?php echo e(route('admin.consoles.edit', $console)); ?>" class="block w-full bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white px-6 py-3 rounded-lg font-semibold text-center transition-all duration-200 transform hover:scale-105">
                            ✏️ Edit Console
                        </a>
                        
                        <!-- Status Update -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="font-semibold text-gray-900 mb-3">Update Status:</h3>
                            <form action="<?php echo e(route('admin.consoles.status', $console)); ?>" method="POST" class="space-y-2">
                                <?php echo csrf_field(); ?>
                                <div class="grid grid-cols-3 gap-2">
                                    <button type="submit" name="status" value="available" class="px-3 py-2 text-sm bg-green-100 text-green-800 rounded-lg hover:bg-green-200 transition-colors duration-200 <?php echo e($console->status === 'available' ? 'ring-2 ring-green-500' : ''); ?>">
                                        Available
                                    </button>
                                    <button type="submit" name="status" value="rented" class="px-3 py-2 text-sm bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 transition-colors duration-200 <?php echo e($console->status === 'rented' ? 'ring-2 ring-blue-500' : ''); ?>">
                                        Rented
                                    </button>
                                    <button type="submit" name="status" value="maintenance" class="px-3 py-2 text-sm bg-yellow-100 text-yellow-800 rounded-lg hover:bg-yellow-200 transition-colors duration-200 <?php echo e($console->status === 'maintenance' ? 'ring-2 ring-yellow-500' : ''); ?>">
                                        Maintenance
                                    </button>
                                </div>
                            </form>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Back Button -->
                    <a href="<?php echo e(route(auth()->user()->isAdmin() ? 'admin.consoles.index' : 'user.consoles.index')); ?>" class="block w-full bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-6 py-3 rounded-lg font-semibold text-center transition-all duration-200">
                        ← Back to Consoles
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Current & Upcoming Bookings -->
    <?php if(auth()->user()->isAdmin()): ?>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Current Booking -->
            <?php if($currentBooking): ?>
                <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
                    <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
                        <svg class="w-6 h-6 mr-2 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
                        </svg>
                        Current Booking
                    </h2>
                    
                    <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-4 border border-green-200">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-semibold text-gray-900"><?php echo e($currentBooking->booking_code); ?></h3>
                            <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                                Active
                            </span>
                        </div>
                        <p class="text-gray-600 mb-2">Customer: <?php echo e($currentBooking->user->name); ?></p>
                        <p class="text-sm text-gray-500">
                            <?php echo e($currentBooking->start_time->format('d M Y, H:i')); ?> - 
                            <?php echo e($currentBooking->end_time->format('d M Y, H:i')); ?>

                        </p>
                        <div class="mt-3">
                            <a href="<?php echo e(route('admin.bookings.show', $currentBooking)); ?>" class="text-blue-600 hover:text-blue-800 font-medium">
                                View Details →
                            </a>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
                    <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
                        <svg class="w-6 h-6 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
                        </svg>
                        Current Booking
                    </h2>
                    
                    <div class="text-center py-8 bg-gray-50 rounded-lg">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-3" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
                        </svg>
                        <p class="text-gray-500">No active booking</p>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Upcoming Bookings -->
            <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
                <h2 class="text-xl font-bold text-gray-900 mb-6 gaming-font flex items-center">
                    <svg class="w-6 h-6 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
                    </svg>
                    Upcoming Bookings
                </h2>
                
                <?php if($upcomingBookings && $upcomingBookings->count() > 0): ?>
                    <div class="space-y-3">
                        <?php $__currentLoopData = $upcomingBookings->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                                <div class="flex items-center justify-between mb-2">
                                    <h3 class="font-semibold text-gray-900"><?php echo e($booking->booking_code); ?></h3>
                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                                        <?php echo e(ucfirst($booking->status)); ?>

                                    </span>
                                </div>
                                <p class="text-gray-600 text-sm mb-1"><?php echo e($booking->user->name); ?></p>
                                <p class="text-xs text-gray-500">
                                    <?php echo e($booking->start_time->format('d M Y, H:i')); ?>

                                </p>
                                <div class="mt-2">
                                    <a href="<?php echo e(route('admin.bookings.show', $booking)); ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        View Details →
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8 bg-gray-50 rounded-lg">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-3" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
                        </svg>
                        <p class="text-gray-500">No upcoming bookings</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <?php if($console->description): ?>
        <!-- Description -->
        <div class="gaming-card rounded-xl shadow-lg p-6 glow-effect">
            <h2 class="text-xl font-bold text-gray-900 mb-4 gaming-font flex items-center">
                <svg class="w-6 h-6 mr-2 text-yellow-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                </svg>
                Description
            </h2>
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p class="text-gray-700"><?php echo e($console->description); ?></p>
            </div>
        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\TokoRentalPlayStation\resources\views/consoles/show.blade.php ENDPATH**/ ?>