<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class SettingsController extends Controller
{
    /**
     * Display store settings
     */
    public function index()
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        $settings = $this->getSettings();

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update store settings
     */
    public function update(Request $request)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'store_name' => 'required|string|max:255',
            'store_description' => 'nullable|string',
            'store_address' => 'nullable|string',
            'store_phone' => 'nullable|string|max:20',
            'store_email' => 'nullable|email|max:255',
            'opening_time' => 'required|date_format:H:i',
            'closing_time' => 'required|date_format:H:i',
            'timezone' => 'required|string',
            'currency' => 'required|string|max:10',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'late_fee_rate' => 'nullable|numeric|min:0',
            'deposit_percentage' => 'nullable|numeric|min:0|max:100',
            'booking_advance_days' => 'required|integer|min:1|max:365',
            'max_booking_duration' => 'required|integer|min:1|max:168',
            'auto_confirm_bookings' => 'boolean',
            'require_deposit' => 'boolean',
            'allow_online_payment' => 'boolean',
            'maintenance_mode' => 'boolean',
            'notification_email' => 'boolean',
            'notification_sms' => 'boolean',
            'reminder_hours_before' => 'required|integer|min:1|max:72',
        ]);

        $settings = $request->all();
        $settings['auto_confirm_bookings'] = $request->has('auto_confirm_bookings');
        $settings['require_deposit'] = $request->has('require_deposit');
        $settings['allow_online_payment'] = $request->has('allow_online_payment');
        $settings['maintenance_mode'] = $request->has('maintenance_mode');
        $settings['notification_email'] = $request->has('notification_email');
        $settings['notification_sms'] = $request->has('notification_sms');

        $this->saveSettings($settings);

        return redirect()->route('admin.settings.index')
            ->with('success', 'Settings updated successfully!');
    }

    /**
     * Update payment methods
     */
    public function updatePaymentMethods(Request $request)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'payment_methods' => 'required|array',
            'payment_methods.*' => 'required|string|in:cash,bank_transfer,credit_card,debit_card,e_wallet,crypto',
        ]);

        $settings = $this->getSettings();
        $settings['payment_methods'] = $request->input('payment_methods');
        $this->saveSettings($settings);

        return response()->json(['success' => true]);
    }

    /**
     * Update pricing rules
     */
    public function updatePricingRules(Request $request)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'pricing_rules' => 'required|array',
            'pricing_rules.*.name' => 'required|string|max:255',
            'pricing_rules.*.type' => 'required|in:discount,surcharge',
            'pricing_rules.*.value' => 'required|numeric|min:0',
            'pricing_rules.*.unit' => 'required|in:percentage,fixed',
            'pricing_rules.*.conditions' => 'nullable|array',
        ]);

        $settings = $this->getSettings();
        $settings['pricing_rules'] = $request->input('pricing_rules');
        $this->saveSettings($settings);

        return response()->json(['success' => true]);
    }

    /**
     * Get all settings with defaults
     */
    private function getSettings()
    {
        $defaults = [
            'store_name' => 'PlayStation Rental Store',
            'store_description' => 'Your premier PlayStation gaming rental destination',
            'store_address' => '',
            'store_phone' => '',
            'store_email' => '',
            'opening_time' => '09:00',
            'closing_time' => '22:00',
            'timezone' => 'Asia/Jakarta',
            'currency' => 'IDR',
            'tax_rate' => 10,
            'late_fee_rate' => 5000,
            'deposit_percentage' => 20,
            'booking_advance_days' => 30,
            'max_booking_duration' => 24,
            'auto_confirm_bookings' => false,
            'require_deposit' => true,
            'allow_online_payment' => true,
            'maintenance_mode' => false,
            'notification_email' => true,
            'notification_sms' => false,
            'reminder_hours_before' => 2,
            'payment_methods' => ['cash', 'bank_transfer', 'e_wallet'],
            'pricing_rules' => [
                [
                    'name' => 'Weekend Surcharge',
                    'type' => 'surcharge',
                    'value' => 20,
                    'unit' => 'percentage',
                    'conditions' => ['weekend' => true]
                ],
                [
                    'name' => 'Long Term Discount',
                    'type' => 'discount',
                    'value' => 15,
                    'unit' => 'percentage',
                    'conditions' => ['min_hours' => 24]
                ]
            ]
        ];

        $settings = Cache::get('store_settings', $defaults);

        // Merge with defaults to ensure all keys exist
        return array_merge($defaults, $settings);
    }

    /**
     * Save settings to cache and file
     */
    private function saveSettings($settings)
    {
        // Save to cache for quick access
        Cache::forever('store_settings', $settings);

        // Save to file for persistence
        Storage::disk('local')->put('settings.json', json_encode($settings, JSON_PRETTY_PRINT));

        // Log the change
        \Log::info('Store settings updated', [
            'user_id' => auth()->id(),
            'settings' => $settings
        ]);
    }

    /**
     * Reset settings to defaults
     */
    public function reset()
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        Cache::forget('store_settings');
        Storage::disk('local')->delete('settings.json');

        return redirect()->route('admin.settings.index')
            ->with('success', 'Settings reset to defaults successfully!');
    }

    /**
     * Export settings
     */
    public function export()
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        $settings = $this->getSettings();

        $filename = 'store_settings_' . now()->format('Y-m-d_H-i-s') . '.json';

        return response()->json($settings)
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    /**
     * Import settings
     */
    public function import(Request $request)
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'settings_file' => 'required|file|mimes:json|max:1024'
        ]);

        try {
            $content = file_get_contents($request->file('settings_file')->path());
            $settings = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Invalid JSON format');
            }

            $this->saveSettings($settings);

            return redirect()->route('admin.settings.index')
                ->with('success', 'Settings imported successfully!');

        } catch (\Exception $e) {
            return redirect()->route('admin.settings.index')
                ->withErrors(['settings_file' => 'Failed to import settings: ' . $e->getMessage()]);
        }
    }

    /**
     * Get setting value by key
     */
    public static function get($key, $default = null)
    {
        $settings = Cache::get('store_settings', []);
        return $settings[$key] ?? $default;
    }

    /**
     * Check if store is open
     */
    public static function isStoreOpen()
    {
        $settings = Cache::get('store_settings', []);

        if ($settings['maintenance_mode'] ?? false) {
            return false;
        }

        $now = now();
        $openTime = \Carbon\Carbon::createFromFormat('H:i', $settings['opening_time'] ?? '09:00');
        $closeTime = \Carbon\Carbon::createFromFormat('H:i', $settings['closing_time'] ?? '22:00');

        return $now->between($openTime, $closeTime);
    }
}
