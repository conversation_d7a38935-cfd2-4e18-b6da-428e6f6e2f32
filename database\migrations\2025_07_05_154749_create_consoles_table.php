<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('consoles', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // PS4, PS5, etc
            $table->string('type'); // PlayStation 4, PlayStation 5
            $table->text('description')->nullable();
            $table->string('image')->nullable();
            $table->decimal('hourly_rate', 8, 2); // Tarif per jam
            $table->decimal('daily_rate', 8, 2); // Tarif per hari
            $table->enum('status', ['available', 'rented', 'maintenance'])->default('available');
            $table->string('serial_number')->unique();
            $table->json('specifications')->nullable(); // Storage, etc
            $table->integer('controller_count')->default(2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('consoles');
    }
};
