<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="bg-gradient-to-r from-blue-900 via-indigo-900 to-purple-900 rounded-2xl shadow-xl p-8 text-white relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full"></div>
            <div class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full"></div>
        </div>
        
        <div class="relative z-10 flex items-center justify-between">
            <div>
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-4">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M21,6H3A1,1 0 0,0 2,7V17A1,1 0 0,0 3,18H21A1,1 0 0,0 22,17V7A1,1 0 0,0 21,6M20,16H4V8H20V16Z"/>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold">PlayStation Consoles</h1>
                        <p class="text-blue-100 text-lg">Manage your gaming consoles</p>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <?php if(auth()->user()->isAdmin()): ?>
                    <a href="<?php echo e(route('admin.consoles.create')); ?>" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 border border-white/30">
                        <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                        </svg>
                        Add Console
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Console Stats -->
    <?php if(auth()->user()->isAdmin()): ?>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white rounded-xl shadow-sm p-6 border-l-4 border-green-500">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100">
                        <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M9,12L11,14L15,10M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2Z"/>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Available</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($consoles->where('status', 'available')->count()); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6 border-l-4 border-blue-500">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100">
                        <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4Z"/>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Rented</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($consoles->where('status', 'rented')->count()); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6 border-l-4 border-yellow-500">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100">
                        <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M11,15H13V17H11V15M11,7H13V13H11V7M12,2C6.47,2 2,6.5 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Maintenance</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($consoles->where('status', 'maintenance')->count()); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6 border-l-4 border-purple-500">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100">
                        <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M21,6H3A1,1 0 0,0 2,7V17A1,1 0 0,0 3,18H21A1,1 0 0,0 22,17V7A1,1 0 0,0 21,6M20,16H4V8H20V16Z"/>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($consoles->count()); ?></p>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Consoles Grid -->
    <div class="bg-white rounded-xl shadow-sm">
        <?php if($consoles->count() > 0): ?>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $consoles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $console): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border-2 border-gray-200 hover:shadow-lg transition-all duration-200 hover:scale-105 <?php echo e($console->status === 'available' ? 'border-green-300' : ($console->status === 'rented' ? 'border-blue-300' : 'border-yellow-300')); ?>">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M21,6H3A1,1 0 0,0 2,7V17A1,1 0 0,0 3,18H21A1,1 0 0,0 22,17V7A1,1 0 0,0 21,6M20,16H4V8H20V16Z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="font-bold text-gray-900"><?php echo e($console->name); ?></h3>
                                        <p class="text-sm text-gray-600"><?php echo e($console->type); ?></p>
                                    </div>
                                </div>
                                
                                <!-- Status Badge -->
                                <?php if($console->status === 'available'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Available
                                    </span>
                                <?php elseif($console->status === 'rented'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Rented
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Maintenance
                                    </span>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Console Details -->
                            <div class="space-y-3 mb-4">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Hourly Rate:</span>
                                    <span class="font-medium text-blue-600">Rp <?php echo e(number_format($console->hourly_rate)); ?></span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Daily Rate:</span>
                                    <span class="font-medium text-blue-600">Rp <?php echo e(number_format($console->daily_rate)); ?></span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Controllers:</span>
                                    <span class="font-medium"><?php echo e($console->controller_count); ?></span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Total Bookings:</span>
                                    <span class="font-medium"><?php echo e($console->total_bookings ?? 0); ?></span>
                                </div>
                            </div>
                            
                            <!-- Specifications -->
                            <?php if($console->specifications): ?>
                                <div class="mb-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Specifications:</h4>
                                    <div class="text-xs text-gray-600 space-y-1">
                                        <?php $__currentLoopData = $console->specifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="flex justify-between">
                                                <span><?php echo e(ucfirst(str_replace('_', ' ', $key))); ?>:</span>
                                                <span><?php echo e($value); ?></span>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Actions -->
                            <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                                <div class="flex items-center space-x-2">
                                    <?php if(auth()->user()->isAdmin()): ?>
                                        <a href="<?php echo e(route('admin.consoles.show', $console)); ?>" class="text-blue-600 hover:text-blue-700 text-sm font-medium">View</a>
                                        <a href="<?php echo e(route('admin.consoles.edit', $console)); ?>" class="text-indigo-600 hover:text-indigo-700 text-sm font-medium">Edit</a>
                                    <?php else: ?>
                                        <a href="<?php echo e(route('user.consoles.show', $console)); ?>" class="text-blue-600 hover:text-blue-700 text-sm font-medium">View Details</a>
                                    <?php endif; ?>
                                </div>
                                
                                <?php if(!auth()->user()->isAdmin() && $console->status === 'available'): ?>
                                    <a href="<?php echo e(route('user.bookings.create', ['console_id' => $console->id])); ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors duration-200">
                                        Book Now
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>

            <!-- Pagination -->
            <?php if($consoles->hasPages()): ?>
                <div class="px-6 py-4 border-t border-gray-200">
                    <?php echo e($consoles->links()); ?>

                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="text-center py-12">
                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M21,6H3A1,1 0 0,0 2,7V17A1,1 0 0,0 3,18H21A1,1 0 0,0 22,17V7A1,1 0 0,0 21,6M20,16H4V8H20V16Z"/>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No consoles found</h3>
                <p class="text-gray-500 mb-4">No PlayStation consoles are available.</p>
                <?php if(auth()->user()->isAdmin()): ?>
                    <a href="<?php echo e(route('admin.consoles.create')); ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                        Add First Console
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\TokoRentalPlayStation\resources\views/consoles/index.blade.php ENDPATH**/ ?>