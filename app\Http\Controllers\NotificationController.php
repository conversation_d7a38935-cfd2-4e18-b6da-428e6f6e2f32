<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Booking;
use App\Models\Console;
use App\Models\User;
use Carbon\Carbon;

class NotificationController extends Controller
{
    /**
     * Get all notifications for admin
     */
    public function index()
    {
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access.');
        }

        $notifications = $this->getAllNotifications();

        return view('admin.notifications.index', compact('notifications'));
    }

    /**
     * Get notifications for API/AJAX
     */
    public function getNotifications()
    {
        $notifications = $this->getAllNotifications();

        return response()->json([
            'notifications' => $notifications,
            'unread_count' => collect($notifications)->where('read', false)->count()
        ]);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Request $request)
    {
        $notificationId = $request->input('notification_id');

        // In a real app, you'd update the notification in database
        // For now, we'll just return success

        return response()->json(['success' => true]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead()
    {
        // In a real app, you'd update all notifications in database

        return response()->json(['success' => true]);
    }

    /**
     * Get return reminders
     */
    public function getReturnReminders()
    {
        $now = now();
        $reminders = [];

        // Bookings ending in 1 hour
        $endingSoon = Booking::with(['user', 'console'])
            ->where('status', 'active')
            ->whereBetween('end_time', [$now, $now->copy()->addHour()])
            ->get();

        foreach ($endingSoon as $booking) {
            $reminders[] = [
                'id' => 'return_soon_' . $booking->id,
                'type' => 'return_reminder',
                'priority' => 'high',
                'title' => 'Return Reminder',
                'message' => "Console {$booking->console->name} should be returned in " .
                           $booking->end_time->diffForHumans() . " by {$booking->user->name}",
                'booking_id' => $booking->id,
                'time' => $booking->end_time,
                'read' => false
            ];
        }

        // Overdue returns
        $overdue = Booking::with(['user', 'console'])
            ->where('status', 'active')
            ->where('end_time', '<', $now)
            ->get();

        foreach ($overdue as $booking) {
            $reminders[] = [
                'id' => 'overdue_' . $booking->id,
                'type' => 'overdue',
                'priority' => 'urgent',
                'title' => 'Overdue Return',
                'message' => "Console {$booking->console->name} is overdue by " .
                           $booking->end_time->diffForHumans() . " from {$booking->user->name}",
                'booking_id' => $booking->id,
                'time' => $booking->end_time,
                'read' => false
            ];
        }

        return $reminders;
    }

    /**
     * Get maintenance alerts
     */
    public function getMaintenanceAlerts()
    {
        $alerts = [];

        // Consoles in maintenance
        $maintenanceConsoles = Console::where('status', 'maintenance')->get();

        foreach ($maintenanceConsoles as $console) {
            $alerts[] = [
                'id' => 'maintenance_' . $console->id,
                'type' => 'maintenance',
                'priority' => 'medium',
                'title' => 'Console Maintenance',
                'message' => "Console {$console->name} is currently under maintenance",
                'console_id' => $console->id,
                'time' => $console->updated_at,
                'read' => false
            ];
        }

        return $alerts;
    }

    /**
     * Get booking alerts
     */
    public function getBookingAlerts()
    {
        $alerts = [];

        // Pending bookings
        $pendingBookings = Booking::with(['user', 'console'])
            ->where('status', 'pending')
            ->where('created_at', '>', now()->subHours(24))
            ->get();

        foreach ($pendingBookings as $booking) {
            $alerts[] = [
                'id' => 'pending_' . $booking->id,
                'type' => 'pending_booking',
                'priority' => 'medium',
                'title' => 'Pending Booking',
                'message' => "New booking {$booking->booking_code} from {$booking->user->name} needs confirmation",
                'booking_id' => $booking->id,
                'time' => $booking->created_at,
                'read' => false
            ];
        }

        // Bookings starting soon
        $startingSoon = Booking::with(['user', 'console'])
            ->where('status', 'confirmed')
            ->whereBetween('start_time', [now(), now()->addHours(2)])
            ->get();

        foreach ($startingSoon as $booking) {
            $alerts[] = [
                'id' => 'starting_soon_' . $booking->id,
                'type' => 'starting_soon',
                'priority' => 'low',
                'title' => 'Booking Starting Soon',
                'message' => "Booking {$booking->booking_code} starts in " .
                           $booking->start_time->diffForHumans(),
                'booking_id' => $booking->id,
                'time' => $booking->start_time,
                'read' => false
            ];
        }

        return $alerts;
    }

    /**
     * Get all notifications combined
     */
    private function getAllNotifications()
    {
        $notifications = array_merge(
            $this->getReturnReminders(),
            $this->getMaintenanceAlerts(),
            $this->getBookingAlerts()
        );

        // Sort by priority and time
        usort($notifications, function ($a, $b) {
            $priorityOrder = ['urgent' => 1, 'high' => 2, 'medium' => 3, 'low' => 4];

            if ($priorityOrder[$a['priority']] !== $priorityOrder[$b['priority']]) {
                return $priorityOrder[$a['priority']] - $priorityOrder[$b['priority']];
            }

            return $b['time']->timestamp - $a['time']->timestamp;
        });

        return $notifications;
    }

    /**
     * Send notification to user (for future implementation)
     */
    public function sendNotification($userId, $type, $message, $data = [])
    {
        // This would integrate with email, SMS, or push notification service
        // For now, we'll just log it

        \Log::info("Notification sent to user {$userId}: {$message}", [
            'type' => $type,
            'data' => $data
        ]);

        return true;
    }
}
